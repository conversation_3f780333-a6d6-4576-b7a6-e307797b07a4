import { env } from './env';
import { formatDate } from './utils';

// Enhanced MarkdownV2 escaping with complete character set
function escapeMarkdownV2(text: string): string {
  return text.replace(/[_*[\\\]()~`>#+=|{}.!-]/g, '\\$&');
}

// Retry configuration
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
};

interface TelegramError {
  ok: false;
  error_code: number;
  description: string;
  parameters?: {
    retry_after?: number;
  };
}

interface TelegramSuccess {
  ok: true;
  result: any;
}

type TelegramResponse = TelegramSuccess | TelegramError;

interface SendResult {
  chatId: string;
  success: boolean;
  error?: string;
  retryAfter?: number;
}

class TelegramService {
  private botToken: string;
  private chatIds: string[];
  private retryConfig: RetryConfig;

  constructor(retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG) {
    this.botToken = env.TELEGRAM_BOT_TOKEN || '';
    this.chatIds = (env.TELEGRAM_CHAT_IDS || '').split(',').map(id => id.trim()).filter(Boolean);
    this.retryConfig = retryConfig;

    if (!this.botToken || this.chatIds.length === 0) {
      console.warn('Telegram service not properly configured. Bot token and chat IDs are required.');
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private calculateBackoffDelay(attempt: number, retryAfter?: number): number {
    if (retryAfter) {
      return Math.min(retryAfter * 1000, this.retryConfig.maxDelay);
    }
    
    const exponentialDelay = this.retryConfig.baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
    return Math.min(exponentialDelay + jitter, this.retryConfig.maxDelay);
  }

  private async sendToTelegram(
    endpoint: string, 
    payload: object, 
    attempt = 1
  ): Promise<TelegramResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

    try {
      const response = await fetch(
        `https://api.telegram.org/bot${this.botToken}/${endpoint}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
          signal: controller.signal,
        }
      );

      clearTimeout(timeoutId);
      const data: TelegramResponse = await response.json();

      // Handle rate limiting and server errors with retry
      if (!data.ok && attempt < this.retryConfig.maxAttempts) {
        const shouldRetry = 
          data.error_code === 429 || // Too Many Requests
          data.error_code >= 500;    // Server errors

        if (shouldRetry) {
          const delay = this.calculateBackoffDelay(attempt, data.parameters?.retry_after);
          console.warn(
            `Telegram API error (attempt ${attempt}/${this.retryConfig.maxAttempts}): ${data.description}. Retrying in ${delay}ms`
          );
          await this.delay(delay);
          return this.sendToTelegram(endpoint, payload, attempt + 1);
        }
      }

      return data;
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      // Retry on network errors
      if (attempt < this.retryConfig.maxAttempts && 
          (error.name === 'AbortError' || error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT')) {
        const delay = this.calculateBackoffDelay(attempt);
        console.warn(
          `Network error (attempt ${attempt}/${this.retryConfig.maxAttempts}): ${error.message}. Retrying in ${delay}ms`
        );
        await this.delay(delay);
        return this.sendToTelegram(endpoint, payload, attempt + 1);
      }

      throw error;
    }
  }

  async sendApplicationNotification(applicationData: {
    name: string;
    email: string;
    phone: string;
    experience: string;
    position: string;
    cvUrl: string;
  }): Promise<{ success: boolean; results: SendResult[]; successCount: number }> {
    const { name, email, phone, experience, position, cvUrl } = applicationData;

    // Check if service is properly configured
    if (!this.botToken || this.chatIds.length === 0) {
      console.error('Telegram service not configured properly');
      return {
        success: false,
        results: [{ chatId: 'config', success: false, error: 'Telegram service not configured' }],
        successCount: 0,
      };
    }

    // Validate CV URL comes from trusted source (basic host check)
    try {
      const url = new URL(cvUrl);
      const allowedHosts = [
        'public.blob.vercel-storage.com', 
        'blob.vercel-storage.com',
        'aqvztdxidpfirjvovhyi.supabase.co',
        'supabase.co'
      ];
      if (!allowedHosts.some(host => url.hostname.endsWith(host))) {
        throw new Error(`CV URL from untrusted host: ${url.hostname}`);
      }
    } catch (error) {
      console.error('Invalid CV URL:', error);
      return {
        success: false,
        results: [{ chatId: 'all', success: false, error: 'Invalid CV URL' }],
        successCount: 0,
      };
    }

    const datePart = formatDate(new Date().toISOString());
    const timePart = '(KSA Time)';
    const caption = [
      '*🔔 New Skillza Job Application*', 
      '',
      `*👤 Name:* ${escapeMarkdownV2(name)}`,
      `*📧 Email:* ${escapeMarkdownV2(email)}`,
      `*📱 Phone:* ${escapeMarkdownV2(phone)}`,
      `*💼 Experience:* ${escapeMarkdownV2(experience)}`,
      `*🎯 Position:* ${escapeMarkdownV2(position)}`,
      '',
      `_Submitted on ${escapeMarkdownV2(datePart)} ${escapeMarkdownV2(timePart)}_`,
    ].join('\n');

    const promises = this.chatIds.map(async (chatId): Promise<SendResult> => {
      try {
        const response = await this.sendToTelegram('sendDocument', {
          chat_id: chatId,
          document: cvUrl,
          caption,
          parse_mode: 'MarkdownV2',
          reply_markup: {
            inline_keyboard: [[
              { text: 'View CV', url: cvUrl },
            ]],
          },
        });

        if (response.ok) {
          return { chatId, success: true };
        } else {
          console.error(
            `Failed to send to chat ${chatId}:`,
            response.description,
            `(Code: ${response.error_code})`
          );
          return {
            chatId,
            success: false,
            error: response.description,
            retryAfter: response.parameters?.retry_after,
          };
        }
      } catch (error: any) {
        console.error(`Network error sending to chat ${chatId}:`, error.message);
        return {
          chatId,
          success: false,
          error: `Network error: ${error.message}`,
        };
      }
    });

    const results = await Promise.all(promises);
    const successCount = results.filter(r => r.success).length;

    return {
      success: successCount > 0,
      results,
      successCount,
    };
  }
}

export const telegramService = new TelegramService();
