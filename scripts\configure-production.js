#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function loadCredentials() {
  log('🔑 Loading credentials from credentials.yaml...', 'cyan');
  
  if (!fs.existsSync('credentials.yaml')) {
    log('❌ credentials.yaml not found!', 'red');
    process.exit(1);
  }
  
  const yaml = fs.readFileSync('credentials.yaml', 'utf8');
  
  // Parse YAML manually (simple format)
  const credentials = {
    telegram: {
      bot_token: '',
      chat_ids: []
    },
    supabase: {
      url: '',
      project_id: '',
      anon_public_key: '',
      service_role_key: ''
    }
  };
  
  const lines = yaml.split('\n');
  let currentSection = '';
  
  lines.forEach(line => {
    const trimmed = line.trim();
    if (trimmed.startsWith('telegram:')) {
      currentSection = 'telegram';
    } else if (trimmed.startsWith('supabase:')) {
      currentSection = 'supabase';
    } else if (trimmed.includes('bot_token:')) {
      credentials.telegram.bot_token = trimmed.split('"')[1];
    } else if (trimmed.includes('- "') && currentSection === 'telegram') {
      credentials.telegram.chat_ids.push(trimmed.split('"')[1]);
    } else if (trimmed.includes('url:')) {
      credentials.supabase.url = trimmed.split('"')[1];
    } else if (trimmed.includes('project_id:')) {
      credentials.supabase.project_id = trimmed.split('"')[1];
    } else if (trimmed.includes('anon_public_key:')) {
      credentials.supabase.anon_public_key = trimmed.split('"')[1];
    } else if (trimmed.includes('service_role_key:')) {
      credentials.supabase.service_role_key = trimmed.split('"')[1];
    }
  });
  
  log(`✅ Credentials loaded successfully`, 'green');
  log(`   Supabase URL: ${credentials.supabase.url}`, 'blue');
  log(`   Project ID: ${credentials.supabase.project_id}`, 'blue');
  log(`   Telegram Bot: ${credentials.telegram.bot_token.substring(0, 10)}...`, 'blue');
  log(`   Chat IDs: ${credentials.telegram.chat_ids.length} configured`, 'blue');
  
  return credentials;
}

function createSupabaseConfig(credentials) {
  log('\n📝 Creating Supabase configuration...', 'cyan');
  
  const config = `[project]
id = "${credentials.supabase.project_id}"
name = "skillza-web"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[functions]
path = "supabase/functions"

[[functions.function]]
name = "upload-cv"
entrypoint = "upload-cv/index.ts"

[[functions.function]]
name = "process-job-application"
entrypoint = "process-job-application/index.ts"

[db]
port = 54322
schemas = ["public", "storage"]
pooler_enabled = false
pooler_port = 54329

[storage]
enabled = true
port = 54323
image_transformation = true
duplicate_functions = false

[auth]
enabled = true
port = 54324
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://skillzajobs.com", "https://www.skillzajobs.com"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true

[edge_runtime]
enabled = true
port = 54325
inspector_port = 54326

[analytics]
enabled = false

[experimental]
webhooks = true
auth_user_create_bucket = true
`;

  if (!fs.existsSync('supabase')) {
    fs.mkdirSync('supabase');
  }
  
  fs.writeFileSync('supabase/config.toml', config);
  log('✅ Supabase config.toml created', 'green');
}

function linkSupabaseProject(credentials) {
  log('\n🔗 Linking to Supabase project...', 'cyan');
  
  try {
    // Check if supabase CLI is installed
    execSync('supabase --version', { stdio: 'pipe' });
  } catch (error) {
    log('❌ Supabase CLI not found. Installing...', 'yellow');
    try {
      execSync('npm install -g supabase', { stdio: 'inherit' });
    } catch (installError) {
      log('❌ Failed to install Supabase CLI. Please install manually:', 'red');
      log('   npm install -g supabase', 'yellow');
      return false;
    }
  }
  
  try {
    // Link the project
    execSync(`supabase link --project-ref ${credentials.supabase.project_id}`, { 
      stdio: 'inherit',
      input: 'y\n' // Auto-confirm
    });
    log('✅ Successfully linked to Supabase project', 'green');
    return true;
  } catch (error) {
    log('⚠️ Could not auto-link project. Please run manually:', 'yellow');
    log(`   supabase link --project-ref ${credentials.supabase.project_id}`, 'cyan');
    return false;
  }
}

function deployEdgeFunctions() {
  log('\n🚀 Deploying Edge Functions...', 'cyan');
  
  const functions = ['upload-cv', 'process-job-application'];
  let deploymentSuccess = true;
  
  functions.forEach(func => {
    try {
      log(`   Deploying ${func}...`, 'blue');
      execSync(`supabase functions deploy ${func}`, { stdio: 'pipe' });
      log(`   ✅ ${func} deployed successfully`, 'green');
    } catch (error) {
      log(`   ❌ Failed to deploy ${func}`, 'red');
      deploymentSuccess = false;
    }
  });
  
  return deploymentSuccess;
}

function setEnvironmentVariables(credentials) {
  log('\n🔧 Setting up environment variables...', 'cyan');
  
  const envVars = [
    {
      name: 'TELEGRAM_BOT_TOKEN',
      value: credentials.telegram.bot_token
    },
    {
      name: 'TELEGRAM_CHAT_IDS',
      value: credentials.telegram.chat_ids.join(',')
    },
    {
      name: 'SUPABASE_SERVICE_ROLE_KEY',
      value: credentials.supabase.service_role_key
    }
  ];
  
  log('Environment variables to set in Supabase Dashboard:', 'yellow');
  log('Go to: Dashboard > Settings > Environment Variables', 'blue');
  log('', 'reset');
  
  envVars.forEach(envVar => {
    log(`${envVar.name}=${envVar.value}`, 'cyan');
  });
  
  log('', 'reset');
  log('💡 Copy these variables to your Supabase project settings', 'yellow');
}

function runMigrations() {
  log('\n📊 Running database migrations...', 'cyan');
  
  try {
    execSync('supabase db push', { stdio: 'inherit' });
    log('✅ Database migrations completed', 'green');
    return true;
  } catch (error) {
    log('❌ Migration failed. You may need to run this manually:', 'red');
    log('   supabase db push', 'yellow');
    return false;
  }
}

function cleanBuildAndGenerate() {
  log('\n🧹 Cleaning and generating production build...', 'cyan');
  
  try {
    // Clean previous builds
    log('   Cleaning previous builds...', 'blue');
    if (fs.existsSync('out')) {
      fs.rmSync('out', { recursive: true, force: true });
    }
    if (fs.existsSync('.next')) {
      fs.rmSync('.next', { recursive: true, force: true });
    }
    
    // Generate fresh build
    log('   Generating production build...', 'blue');
    execSync('npm run deploy:hostinger', { stdio: 'inherit' });
    
    log('✅ Production build completed successfully', 'green');
    return true;
  } catch (error) {
    log('❌ Build failed:', 'red');
    log(error.message, 'red');
    return false;
  }
}

function testConfiguration(credentials) {
  log('\n🧪 Testing configuration...', 'cyan');
  
  const tests = [
    {
      name: 'Environment file exists',
      test: () => fs.existsSync('.env.local')
    },
    {
      name: 'Build output exists',
      test: () => fs.existsSync('out/index.html')
    },
    {
      name: 'Jobs data included',
      test: () => fs.existsSync('out/jobs.json')
    },
    {
      name: 'HTACCESS file created',
      test: () => fs.existsSync('out/.htaccess')
    },
    {
      name: 'Supabase functions exist',
      test: () => fs.existsSync('supabase/functions/upload-cv/index.ts') && 
                   fs.existsSync('supabase/functions/process-job-application/index.ts')
    }
  ];
  
  let allPassed = true;
  tests.forEach(test => {
    const passed = test.test();
    log(`   ${passed ? '✅' : '❌'} ${test.name}`, passed ? 'green' : 'red');
    if (!passed) allPassed = false;
  });
  
  return allPassed;
}

async function main() {
  log('🚀 Skillza Production Configuration', 'bright');
  log('===================================', 'bright');
  
  try {
    // Load credentials
    const credentials = loadCredentials();
    
    // Configure Supabase
    createSupabaseConfig(credentials);
    
    // Link project
    const linked = linkSupabaseProject(credentials);
    
    if (linked) {
      // Deploy functions
      deployEdgeFunctions();
      
      // Run migrations
      runMigrations();
    }
    
    // Set environment variables (manual step)
    setEnvironmentVariables(credentials);
    
    // Clean and build
    const buildSuccess = cleanBuildAndGenerate();
    
    if (buildSuccess) {
      // Test everything
      const testsPass = testConfiguration(credentials);
      
      if (testsPass) {
        log('\n🎉 Production setup completed successfully!', 'green');
        log('📋 Next Steps:', 'cyan');
        log('1. Set environment variables in Supabase Dashboard', 'reset');
        log('2. Upload `out/` directory to Hostinger', 'reset');
        log('3. Test the complete application flow', 'reset');
        log('4. Your application is ready for production! 🚀', 'bright');
      } else {
        log('\n⚠️ Some tests failed. Please check the issues above.', 'yellow');
      }
    }
    
  } catch (error) {
    log('\n❌ Setup failed:', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
