import Image from "next/image"
import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"
import { Suspense } from "react"
import { Toaster } from "@/components/ui/toaster"
import { LanguageProvider } from "@/lib/language-context"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ErrorBoundary } from "@/components/error-boundary"
import "./globals.css"
import "./rtl.css"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
  preload: true,
})

const tajawal = Tajawal({
  subsets: ["arabic"],
  weight: ["400", "500", "700"], // Reduced weights for better performance
  variable: "--font-tajawal",
  display: "swap",
  preload: true,
})

export const metadata: Metadata = {
  title: {
    template: "%s | Skillza",
    default: "Skillza - Specialized Recruitment Services in Saudi Arabia"
  },
  description:
    "اكتشف فرص العمل المتميزة مع سيلزا، شريكك في التوظيف المتخصص. نوفر أفضل الوظائف في جميع القطاعات بالمملكة العربية السعودية.",
  keywords: [
    "وظائف Skillza",
    "وظائف السعودية",
    "خدمات التوظيف",
    "شركة توظيف",
    "وظائف متخصصة",
    "وظائف هندسة",
    "وظائف مالية",
    "وظائف موارد بشرية",
    "وظائف تكنولوجيا",
    "وظائف تسويق",
    "وظائف قانونية",
    "وظائف تعليم",
    "Skillza careers",
    "Saudi Arabia jobs",
    "recruitment services",
    "specialized jobs"
  ],
  authors: [{ name: "Skillza" }],
  creator: "Skillza",
  publisher: "Skillza",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"),
  alternates: {
    canonical: "/",
    languages: {
      "ar-SA": "/",
      "en-US": "/",
    },
  },
  openGraph: {
    title: "Skillza - Specialized Recruitment Services in Saudi Arabia",
    description:
      "اكتشف فرص العمل المتميزة مع سيلزا، شريكك في التوظيف المتخصص. نوفر أفضل الوظائف في جميع القطاعات بالمملكة العربية السعودية.",
    type: "website",
    locale: "ar_SA",
    alternateLocale: "en_US",
    url: "/",
    siteName: "Skillza Careers",
    
  },
  twitter: {
    card: "summary_large_image",
    title: "Skillza - Specialized Recruitment Services",
    description: "اكتشف فرص العمل المتميزة مع سيلزا، شريكك في التوظيف المتخصص في المملكة العربية السعودية.",
    
    creator: "@skillza_sa"
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: "/favicon.ico",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/manifest.webmanifest",
  category: "business",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning className={`${inter.variable} ${tajawal.variable}`}>
      <head>
        <meta name="theme-color" content="#6366F1" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        {/* Resource hints for performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//aqvztdxidpfirjvovhyi.supabase.co" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        {/* JSON-LD structured data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "Skillza",
              "url": "https://skillzajobs.com",
              "logo": "https://skillzajobs.com/apple-touch-icon.png",
              "description": "Specialized recruitment services in Saudi Arabia",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "SA",
                "addressLocality": "Riyadh",
                "addressRegion": "Riyadh Province"
              },
              "sameAs": [
                "https://twitter.com/skillza_sa"
              ]
            })
          }}
        />
        {/* Critical CSS for font display optimization */}
        <style dangerouslySetInnerHTML={{
          __html: `
            @font-face {
              font-family: 'Inter var';
              font-style: normal;
              font-weight: 100 900;
              font-display: swap;
              src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2) format('woff2');
            }
            .font-sans { font-family: 'Inter var', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; }
            body { font-display: swap; }
          `
        }} />
      </head>
      <body className="font-arabic antialiased">
        <ErrorBoundary>
          <LanguageProvider>
            <Navbar />
            <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>
            <Toaster />
            <Footer />
          </LanguageProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
