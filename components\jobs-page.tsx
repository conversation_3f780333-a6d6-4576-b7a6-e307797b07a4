"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Search, MapPin, Calendar, DollarSign, Briefcase } from "lucide-react"
import { useLanguage } from "@/lib/language-context"
import Link from "next/link"

interface Job {
  id: string
  title: {
    ar: string
    en: string
  }
  sector: string
  location: {
    ar: string
    en: string
  }
  description: {
    ar: string
    en: string
  }
  requirements: {
    ar: string[]
    en: string[]
  }
  postedDate: string
  type: {
    ar: string
    en: string
  }
  salary: {
    ar: string
    en: string
  }
}

export function JobsPage({ jobs: initialJobs }: { jobs: Job[] }) {
  const { t, language, isRTL } = useLanguage()
  const [jobs, setJobs] = useState<Job[]>(initialJobs)
  const [filteredJobs, setFilteredJobs] = useState<Job[]>(initialJobs)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSector, setSelectedSector] = useState("all")
  const [loading, setLoading] = useState(false)

  

  useEffect(() => {
    let result = jobs

    // Filter by search term
    if (searchTerm) {
      result = result.filter(job => 
        job.title[language].toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.description[language].toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.requirements[language].some(req => req.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Filter by sector
    if (selectedSector !== "all") {
      result = result.filter(job => job.sector === selectedSector)
    }

    setFilteredJobs(result)
  }, [searchTerm, selectedSector, jobs, language])

  // Get unique sectors
  const sectors = Array.from(new Set(jobs.map(job => job.sector)))

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white py-12">
      <div className="container mx-auto px-4">
        <div className="mb-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4 font-arabic">
              {language === "ar" ? "الوظائف المتاحة" : "Open Positions"}
            </h1>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto font-arabic">
              {language === "ar" 
                ? "انضم إلى فريق سيلزا وابن مستقبلك معنا" 
                : "Join the Skillza team and build your future with us"}
            </p>
          </div>

          {/* Search and Filter Section */}
          <Card className="mb-12 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Search Input */}
                <div className="md:col-span-2">
                  <Label htmlFor="search" className="text-sm font-medium mb-2 block text-right">
                    {language === "ar" ? "البحث عن وظائف" : "Search Jobs"}
                  </Label>
                  <div className="relative">
                    <Search className="absolute top-3 right-3 text-slate-400" size={20} />
                    <Input
                      id="search"
                      placeholder={language === "ar" ? "ابحث بالعنوان أو المهارات..." : "Search by title or skills..."}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-10 text-right font-arabic"
                    />
                  </div>
                </div>

                {/* Sector Filter */}
                <div>
                  <Label htmlFor="sector" className="text-sm font-medium mb-2 block text-right">
                    {language === "ar" ? "القطاع" : "Sector"}
                  </Label>
                  <Select value={selectedSector} onValueChange={setSelectedSector} name="sector">
                    <SelectTrigger id="sector" className="text-right font-arabic">
                      <SelectValue 
                        placeholder={language === "ar" ? "اختر القطاع" : "Select sector"} 
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        {language === "ar" ? "الكل" : "All"}
                      </SelectItem>
                      {sectors.map((sector) => (
                        <SelectItem key={sector} value={sector}>
                          {sector}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Jobs Listings */}
          {filteredJobs.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-slate-700 mb-2 font-arabic">
                {language === "ar" ? "لم يتم العثور على وظائف" : "No jobs found"}
              </h3>
              <p className="text-slate-500 font-arabic">
                {language === "ar" 
                  ? "جرّب تعديل معايير البحث" 
                  : "Try adjusting your search criteria"}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredJobs.map((job) => (
                <Card 
                  key={job.id} 
                  className="hover:shadow-xl transition-all duration-300 border-slate-200 overflow-hidden"
                >
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-xl font-bold text-slate-900 font-arabic text-right">
                        {job.title[language]}
                      </CardTitle>
                      <Badge 
                        variant="secondary" 
                        className="bg-indigo-100 text-indigo-800"
                      >
                        {job.type[language]}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-slate-600 mt-2 flex-row-reverse">
                      <Briefcase size={16} />
                      <span className="font-arabic">
                        {job.sector}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-right">
                      <div className="flex items-center gap-2">
                        <MapPin size={16} className="text-slate-500" />
                        <span className="text-slate-700 font-arabic">
                          {job.location[language]}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <DollarSign size={16} className="text-slate-500" />
                        <span className="text-slate-700 font-arabic">
                          {job.salary[language]}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar size={16} className="text-slate-500" />
                        <span className="text-slate-700 font-arabic">
                          {language === "ar" ? "نُشر في" : "Posted"} {new Date(job.postedDate).toLocaleDateString(language === "ar" ? "ar-SA" : "en-US")}
                        </span>
                      </div>
                      
                      <p className="text-slate-600 line-clamp-2 font-arabic text-right">
                        {job.description[language]}
                      </p>
                      
                      <div className="pt-4">
                        <Link href={`/apply?job=${job.id}`}>
                          <Button className="w-full bg-indigo-600 hover:bg-indigo-700">
                            {language === "ar" ? "تقدم للوظيفة" : "Apply Now"}
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}