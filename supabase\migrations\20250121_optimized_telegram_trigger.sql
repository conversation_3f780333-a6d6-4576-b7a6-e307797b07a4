-- Optimized Telegram notification trigger for 2025
-- This migration creates a streamlined database trigger for Telegram notifications

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS job_application_notification_trigger ON public.job_applications;

-- Create optimized notification function
CREATE OR REPLACE FUNCTION notify_telegram_optimized()
RETURNS TRIGGER AS $$
BEGIN
  -- Use http_post to call the optimized telegram-notification function
  PERFORM http_post(
    'https://aqvztdxidpfirjvovhyi.functions.supabase.co/telegram-notification',
    json_build_object(
      'record', json_build_object(
        'name', NEW.name,
        'email', NEW.email,
        'phone', NEW.phone,
        'experience', NEW.experience,
        'position', NEW.position,
        'cv_url', NEW.cv_url,
        'job_id', NEW.job_id,
        'language', NEW.language,
        'submitted_at', NEW.submitted_at
      )
    )::text,
    'application/json',
    ARRAY[
      http_header('Authorization', 'Bearer ' || current_setting('supabase.auth.jwt')),
      http_header('Content-Type', 'application/json')
    ]
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the insert
    RAISE LOG 'Failed to send Telegram notification: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the optimized trigger
CREATE TRIGGER job_application_notification_trigger
  AFTER INSERT ON public.job_applications
  FOR EACH ROW
  EXECUTE FUNCTION notify_telegram_optimized();

-- Add comment for documentation
COMMENT ON TRIGGER job_application_notification_trigger ON public.job_applications 
IS 'Sends Telegram notifications for new job applications using optimized edge function';
