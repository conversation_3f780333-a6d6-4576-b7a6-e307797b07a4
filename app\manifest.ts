import type { MetadataRoute } from "next"

export const dynamic = "force-static"

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "Skillza - Specialized Recruitment Services",
    short_name: "<PERSON><PERSON><PERSON>",
    description: "Discover outstanding job opportunities with <PERSON><PERSON><PERSON>, your specialized recruitment partner in Saudi Arabia.",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#6366F1",
    orientation: "portrait-primary",
    scope: "/",
    icons: [
      {
        src: "/icons/icon-192.jpg",
        sizes: "192x192",
        type: "image/jpeg",
        purpose: "maskable",
      },
      {
        src: "/icons/icon-512.jpg",
        sizes: "512x512",
        type: "image/jpeg",
        purpose: "any",
      },
    ],
    categories: ["business", "productivity", "utilities"],
    lang: "ar",
    dir: "rtl",
    related_applications: [],
    prefer_related_applications: false,
  }
}
