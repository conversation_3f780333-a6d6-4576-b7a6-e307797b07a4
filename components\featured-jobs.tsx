"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Calendar, DollarSign, Briefcase } from "lucide-react"
import { useLanguage } from "@/lib/language-context"
import Link from "next/link"

interface Job {
  id: string
  title: {
    ar: string
    en: string
  }
  sector: string
  location: {
    ar: string
    en: string
  }
  description: {
    ar: string
    en: string
  }
  requirements: {
    ar: string[]
    en: string[]
  }
  postedDate: string
  type: {
    ar: string
    en: string
  }
  salary: {
    ar: string
    en: string
  }
}

export function FeaturedJobs() {
  const { language, isRTL } = useLanguage()
  const [jobs, setJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        // For static hosting, use jobs.json file
        const response = await fetch("/jobs.json")
        if (!response.ok) {
          throw new Error(`Failed to fetch jobs: ${response.status}`)
        }
        const data = await response.json()
        // Get first 3 jobs as featured
        setJobs(data.slice(0, 3))
        setLoading(false)
      } catch (error) {
        console.error("Error fetching jobs:", error)
        setLoading(false)
      }
    }

    fetchJobs()
  }, [])

  if (loading) {
    return (
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-slate-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="h-64 animate-pulse">
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                  <div className="h-4 bg-slate-200 rounded w-full"></div>
                  <div className="h-4 bg-slate-200 rounded w-5/6"></div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className={`text-3xl lg:text-5xl font-bold text-slate-900 mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "وظائف مميزة" : "Featured Jobs"}
          </h2>
          <p className={`text-xl text-slate-600 max-w-3xl mx-auto ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "اكتشف أفضل الفرص الوظيفية المتاحة في سيلزا" 
              : "Discover the best job opportunities available at Skillza"}
          </p>
        </div>

        {jobs.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {jobs.map((job) => (
              <Card 
                key={job.id} 
                className="hover:shadow-xl transition-all duration-300 border-slate-200 overflow-hidden h-full flex flex-col"
              >
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <CardTitle className={`text-xl font-bold text-slate-900 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                      {job.title[language]}
                    </CardTitle>
                    <Badge 
                      variant="secondary" 
                      className="bg-indigo-100 text-indigo-800"
                    >
                      {job.type[language]}
                    </Badge>
                  </div>
                  <div className={`flex items-center gap-2 text-slate-600 mt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Briefcase size={16} />
                    <span className={isRTL ? "font-arabic" : "font-sans"}>
                      {job.sector}
                    </span>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow">
                  <div className={`space-y-3 ${isRTL ? "text-right" : ""}`}>
                    <div className="flex items-center gap-2">
                      <MapPin size={16} className="text-slate-500" />
                      <span className={`text-slate-700 ${isRTL ? "font-arabic" : "font-sans"}`}>
                        {job.location[language]}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <DollarSign size={16} className="text-slate-500" />
                      <span className={`text-slate-700 ${isRTL ? "font-arabic" : "font-sans"}`}>
                        {job.salary[language]}
                      </span>
                    </div>
                    
                    <p className={`text-slate-600 line-clamp-2 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                      {job.description[language]}
                    </p>
                  </div>
                </CardContent>
                <div className="px-6 pb-6">
                  <Link href={`/apply?job=${job.id}`}>
                    <Button className="w-full bg-indigo-600 hover:bg-indigo-700">
                      {language === "ar" ? "تقدم الآن" : "Apply Now"}
                    </Button>
                  </Link>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className={`text-slate-600 text-lg ${isRTL ? "font-arabic" : "font-sans"}`}>
              {language === "ar" ? "لا توجد وظائف متوفرة حاليًا" : "No jobs available at the moment"}
            </p>
          </div>
        )}

        <div className="text-center mt-12">
          <Link href="/jobs">
            <Button 
              size="lg" 
              variant="outline" 
              className="border-indigo-600 text-indigo-600 hover:bg-indigo-50 px-8 py-3 text-lg font-semibold"
            >
              {language === "ar" ? "عرض جميع الوظائف" : "View All Jobs"}
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}