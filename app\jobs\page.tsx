'use client';

import { JobsPage } from "@/components/jobs-page";
import { useEffect, useState } from "react";
import { logger } from "@/lib/logger";
import { createClient } from "@supabase/supabase-js";

interface Job {
  id: string;
  title: { ar: string; en: string };
  sector: string;
  location: { ar: string; en: string };
  description: { ar: string; en: string };
  requirements: { ar: string[]; en: string[] };
  posted_date: string;
  type: { ar: string; en: string };
  salary: { ar: string; en: string };
}

export default function Jobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchJobs() {
      try {
        // For static hosting, use jobs.json file
        const response = await fetch('/jobs.json');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch jobs: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setJobs(data || []);
      } catch (err) {
        logger.error('Error fetching jobs:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
      } finally {
        setLoading(false);
      }
    }

    fetchJobs();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <main className="flex-grow pt-20 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p className="text-slate-600">Loading jobs...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        <main className="flex-grow pt-20 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">⚠️</div>
            <p className="text-red-600 mb-4">Failed to load jobs</p>
            <p className="text-slate-600 text-sm">{error}</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-grow pt-20">
        <JobsPage jobs={jobs} />
      </main>
    </div>
  );
}
