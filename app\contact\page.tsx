"use client"

import { useLanguage } from "@/lib/language-context"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  User, 
  MessageSquare 
} from "lucide-react"
import { useState } from "react"

export default function ContactPage() {
  const { language, isRTL } = useLanguage()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Form submission logic would go here
    alert(language === "ar" 
      ? "شكرًا لرسالتك! سنقوم بالرد عليك في أقرب وقت ممكن." 
      : "Thank you for your message! We'll get back to you as soon as possible."
    )
    setFormData({ name: "", email: "", subject: "", message: "" })
  }

  const contactInfo = [
    {
      icon: MapPin,
      title: language === "ar" ? "العنوان" : "Address",
      content: language === "ar" 
        ? "الرياض، المملكة العربية السعودية" 
        : "Riyadh, Saudi Arabia"
    },
    {
      icon: Phone,
      title: language === "ar" ? "رقم الهاتف" : "Phone Number",
      content: "+966 11 123 4567"
    },
    {
      icon: Mail,
      title: language === "ar" ? "البريد الإلكتروني" : "Email",
      content: "<EMAIL>"
    },
    {
      icon: Clock,
      title: language === "ar" ? "ساعات العمل" : "Working Hours",
      content: language === "ar" 
        ? "الأحد - الخميس: 8:00 صباحاً - 5:00 مساءً" 
        : "Sunday - Thursday: 8:00 AM - 5:00 PM"
    }
  ]

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className={`inline-block px-3 py-1 rounded-full bg-indigo-100 text-indigo-800 text-sm font-medium mb-4 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "تواصل معنا" : "Contact Us"}
          </span>
          <h1 className={`text-4xl lg:text-5xl font-bold text-slate-900 mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "نحن هنا للمساعدة" : "We're Here to Help"}
          </h1>
          <p className={`text-xl text-slate-600 max-w-3xl mx-auto ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "هل لديك أي أسئلة أو استفسارات؟ لا تتردد في التواصل معنا في أي وقت. فريقنا مستعد لمساعدتك." 
              : "Have any questions or inquiries? Don't hesitate to reach out to us at any time. Our team is ready to assist you."}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50">
              <CardHeader>
                <CardTitle className={`text-2xl font-bold text-slate-900 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {language === "ar" ? "معلومات الاتصال" : "Contact Information"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {contactInfo.map((info, index) => {
                    const IconComponent = info.icon
                    return (
                      <div key={index} className="flex items-start gap-4">
                        <div className="bg-indigo-100 p-3 rounded-lg flex-shrink-0">
                          <IconComponent className="text-indigo-600" size={24} />
                        </div>
                        <div>
                          <h3 className={`font-semibold text-lg text-slate-900 mb-1 ${isRTL ? "font-arabic" : "font-sans"}`}>
                            {info.title}
                          </h3>
                          <p className={`text-slate-600 ${isRTL ? "font-arabic" : "font-sans"}`}>
                            {info.content}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
                
                <div className="mt-12">
                  <h3 className={`text-xl font-bold text-slate-900 mb-4 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    {language === "ar" ? "لماذا تختار سكيلزا؟" : "Why Choose Skillza?"}
                  </h3>
                  <ul className={`space-y-3 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    <li className="flex items-start gap-2">
                      <div className="mt-1 w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0"></div>
                      <span className="text-slate-600">
                        {language === "ar" 
                          ? "شبكة واسعة من الشركاء والشركات الرائدة" 
                          : "Extensive network of partner companies"}
                      </span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="mt-1 w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0"></div>
                      <span className="text-slate-600">
                        {language === "ar" 
                          ? "فريق متخصص من مستشاري التوظيف" 
                          : "Specialized team of recruitment consultants"}
                      </span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="mt-1 w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0"></div>
                      <span className="text-slate-600">
                        {language === "ar" 
                          ? "خدمات توظيف شاملة تغطي جميع القطاعات" 
                          : "Comprehensive recruitment services across all sectors"}
                      </span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="mt-1 w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0"></div>
                      <span className="text-slate-600">
                        {language === "ar" 
                          ? "نتائج سريعة وفعالة في ربط المواهب بالفرص المناسبة" 
                          : "Fast and effective results connecting talent with opportunities"}
                      </span>
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div>
            <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50">
              <CardHeader>
                <CardTitle className={`text-2xl font-bold text-slate-900 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {language === "ar" ? "أرسل لنا رسالة" : "Send Us a Message"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label 
                      htmlFor="name" 
                      className={`block text-sm font-medium text-slate-700 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}
                    >
                      {language === "ar" ? "الاسم الكامل" : "Full Name"}
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder={language === "ar" ? "أدخل اسمك الكامل" : "Enter your full name"}
                        className={`pr-10 pl-10 py-6 ${isRTL ? "font-arabic text-right" : "font-sans"}`}
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label 
                      htmlFor="email" 
                      className={`block text-sm font-medium text-slate-700 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}
                    >
                      {language === "ar" ? "البريد الإلكتروني" : "Email Address"}
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder={language === "ar" ? "<EMAIL>" : "<EMAIL>"}
                        className={`pr-10 pl-10 py-6 ${isRTL ? "font-arabic text-right" : "font-sans"}`}
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label 
                      htmlFor="subject" 
                      className={`block text-sm font-medium text-slate-700 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}
                    >
                      {language === "ar" ? "الموضوع" : "Subject"}
                    </label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder={language === "ar" ? "موضوع رسالتك" : "Subject of your message"}
                      className={`py-6 ${isRTL ? "font-arabic text-right" : "font-sans"}`}
                      required
                    />
                  </div>
                  
                  <div>
                    <label 
                      htmlFor="message" 
                      className={`block text-sm font-medium text-slate-700 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}
                    >
                      {language === "ar" ? "الرسالة" : "Message"}
                    </label>
                    <div className="relative">
                      <MessageSquare className="absolute left-3 top-3 text-slate-400" size={18} />
                      <Textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder={language === "ar" ? "اكتب رسالتك هنا..." : "Write your message here..."}
                        rows={6}
                        className={`pr-4 pl-10 py-4 ${isRTL ? "font-arabic text-right" : "font-sans"}`}
                        required
                      />
                    </div>
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-6 text-lg font-semibold transition-all"
                  >
                    {language === "ar" ? "إرسال الرسالة" : "Send Message"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      </main>
      <Footer />
    </div>
  )
}
