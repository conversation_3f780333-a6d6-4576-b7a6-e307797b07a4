import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { logger } from '@/lib/logger';

// Simplified notification handler for static deployment
// Notifications are processed automatically by the system

const ApplicationSchema = z.object({
  name: z.string().min(1).max(80),
  email: z.string().email().max(254),
  phone: z.string().min(1),
  experience: z.string().min(1),
  position: z.string().min(1).max(200),
  cvUrl: z.string().url(),
});

export async function POST(request: NextRequest) {
  logger.info('Notify API called');
  
  try {
    const body = await request.json();
    const validationResult = ApplicationSchema.safeParse(body);
    
    if (!validationResult.success) {
      logger.error('Validation failed:', validationResult.error);
      return NextResponse.json(
        { 
          ok: false, 
          error: 'Invalid form data',
          details: validationResult.error.flatten().fieldErrors 
        },
        { status: 400 }
      );
    }

    const applicationData = validationResult.data;
    logger.debug('Application data received:', {
      name: applicationData.name,
      email: applicationData.email,
      position: applicationData.position
    });

    // For static deployment, we'll return success
    // Notifications are handled automatically by the system
    
    return NextResponse.json({
      ok: true,
      message: 'Application processed successfully'
    });

  } catch (error: any) {
    logger.error('Notify API error:', error);
    return NextResponse.json(
      { 
        ok: false, 
        error: 'Failed to process notification' 
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
}
