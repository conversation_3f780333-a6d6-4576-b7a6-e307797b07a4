-- Create job_applications table
CREATE TABLE IF NOT EXISTS job_applications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON>HAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50) NOT NULL,
  experience VARCHAR(50) NOT NULL,
  position VARCHAR(255) NOT NULL,
  cv_url TEXT NOT NULL,
  job_id VARCHAR(50),
  language VARCHAR(10) DEFAULT 'en',
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_job_applications_submitted_at ON job_applications(submitted_at);
CREATE INDEX IF NOT EXISTS idx_job_applications_job_id ON job_applications(job_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_email ON job_applications(email);

-- Create storage bucket for CVs
INSERT INTO storage.buckets (id, name, public) 
VALUES ('cvs', 'cvs', true)
ON CONFLICT (id) DO NOTHING;

-- Set up RLS (Row Level Security)
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;

-- Policy to allow service role to do everything
CREATE POLICY "Service role can manage job_applications" ON job_applications
  FOR ALL USING (auth.role() = 'service_role');

-- Policy to allow anon users to insert (for form submissions)
CREATE POLICY "Allow anon to insert applications" ON job_applications
  FOR INSERT WITH CHECK (true);

-- Storage policies for CV uploads
CREATE POLICY "Anyone can upload CVs" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'cvs');

CREATE POLICY "Anyone can view CVs" ON storage.objects
  FOR SELECT USING (bucket_id = 'cvs');

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_job_applications_updated_at 
  BEFORE UPDATE ON job_applications 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger function for Telegram notifications
CREATE OR REPLACE FUNCTION notify_telegram()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM net.http_post(
    'https://aqvztdxidpfirjvovhyi.functions.supabase.co/send-telegram-notification',
    json_build_object('record', NEW)::text,
    'application/json',
    ARRAY[http_header('Authorization', 'Bearer ' || current_setting('supabase.auth.jwt'))]
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for job applications
CREATE TRIGGER job_application_insert_trigger
AFTER INSERT ON public.job_applications
FOR EACH ROW
EXECUTE FUNCTION notify_telegram();