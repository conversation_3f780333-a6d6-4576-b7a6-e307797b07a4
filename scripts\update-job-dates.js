#!/usr/bin/env node

/**
 * Job Date Updater Script
 * Randomly updates job posting dates to be within the last week
 * This keeps job listings appearing fresh and current
 */

const fs = require('fs');
const path = require('path');

// Configuration
const JOBS_FILE_PATH = path.join(__dirname, '..', 'public', 'jobs.json');
const MAX_DAYS_AGO = 7; // Maximum days ago (1 week)
const MIN_DAYS_AGO = 1; // Minimum days ago (yesterday)

/**
 * Generate a random date within the last week
 * @returns {string} Date in YYYY-MM-DD format
 */
function generateRandomDate() {
  const now = new Date();
  const maxDaysAgo = MAX_DAYS_AGO;
  const minDaysAgo = MIN_DAYS_AGO;
  
  // Generate random number of days ago (between min and max)
  const randomDaysAgo = Math.floor(Math.random() * (maxDaysAgo - minDaysAgo + 1)) + minDaysAgo;
  
  // Calculate the random date
  const randomDate = new Date(now);
  randomDate.setDate(now.getDate() - randomDaysAgo);
  
  // Format as YYYY-MM-DD
  return randomDate.toISOString().split('T')[0];
}

/**
 * Update job posting dates in the jobs.json file
 */
function updateJobDates() {
  try {
    console.log('🔄 Updating job posting dates...');
    
    // Read the jobs.json file
    if (!fs.existsSync(JOBS_FILE_PATH)) {
      throw new Error(`Jobs file not found at: ${JOBS_FILE_PATH}`);
    }
    
    const jobsData = JSON.parse(fs.readFileSync(JOBS_FILE_PATH, 'utf8'));
    
    if (!Array.isArray(jobsData)) {
      throw new Error('Invalid jobs data format. Expected an array.');
    }
    
    // Update each job's postedDate
    let updatedCount = 0;
    const updatedJobs = jobsData.map(job => {
      if (job.postedDate) {
        const newDate = generateRandomDate();
        console.log(`📅 Updating job "${job.title?.en || job.title?.ar || job.id}" from ${job.postedDate} to ${newDate}`);
        updatedCount++;
        return {
          ...job,
          postedDate: newDate
        };
      }
      return job;
    });
    
    // Write the updated data back to the file
    fs.writeFileSync(JOBS_FILE_PATH, JSON.stringify(updatedJobs, null, 2), 'utf8');
    
    console.log(`✅ Successfully updated ${updatedCount} job posting dates`);
    console.log(`📊 Total jobs processed: ${updatedJobs.length}`);
    
    // Show some statistics
    const dates = updatedJobs.map(job => job.postedDate).filter(Boolean);
    const uniqueDates = [...new Set(dates)];
    console.log(`📈 Unique posting dates: ${uniqueDates.length}`);
    
    if (uniqueDates.length > 0) {
      const sortedDates = uniqueDates.sort();
      console.log(`📅 Date range: ${sortedDates[0]} to ${sortedDates[sortedDates.length - 1]}`);
    }
    
  } catch (error) {
    console.error('❌ Error updating job dates:', error.message);
    process.exit(1);
  }
}

/**
 * Validate the updated jobs data
 */
function validateJobsData() {
  try {
    const jobsData = JSON.parse(fs.readFileSync(JOBS_FILE_PATH, 'utf8'));
    
    if (!Array.isArray(jobsData)) {
      throw new Error('Invalid jobs data format');
    }
    
    // Check if all jobs have valid postedDate
    const invalidJobs = jobsData.filter(job => {
      if (!job.postedDate) return true;
      const date = new Date(job.postedDate);
      return isNaN(date.getTime());
    });
    
    if (invalidJobs.length > 0) {
      console.warn(`⚠️  Found ${invalidJobs.length} jobs with invalid dates`);
    } else {
      console.log('✅ All job dates are valid');
    }
    
  } catch (error) {
    console.error('❌ Validation error:', error.message);
    process.exit(1);
  }
}

// Main execution
if (require.main === module) {
  console.log('🚀 Starting job date update process...');
  console.log(`📁 Jobs file: ${JOBS_FILE_PATH}`);
  console.log(`📅 Date range: ${MIN_DAYS_AGO}-${MAX_DAYS_AGO} days ago`);
  console.log('');
  
  updateJobDates();
  console.log('');
  validateJobsData();
  
  console.log('');
  console.log('🎉 Job date update completed successfully!');
}

module.exports = {
  updateJobDates,
  generateRandomDate,
  validateJobsData
};
