
import { NextResponse } from "next/server";
import { z } from "zod";

export function withValidation(schema: z.ZodSchema<any, any, any>, handler: (req: Request, data: any, ...args: any[]) => Promise<NextResponse>) {
  return async (req: Request, ...args: any[]) => {
    const body = await req.json();
    const validation = schema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({ error: "Invalid input", details: validation.error.flatten() }, { status: 400 });
    }
    return handler(req, validation.data, ...args);
  };
}
