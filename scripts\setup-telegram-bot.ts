import * as dotenv from 'dotenv';
dotenv.config();

async function setupTelegramBot() {
  const adminApiToken = process.env.ADMIN_API_TOKEN;
  const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN;
  const telegramWebhookSecret = process.env.TELEGRAM_WEBHOOK_SECRET;

  if (!adminApiToken) {
    console.error('Error: ADMIN_API_TOKEN is not set in environment variables.');
    process.exit(1);
  }

  if (!telegramBotToken) {
    console.error('Error: TELEGRAM_BOT_TOKEN is not set in environment variables.');
    process.exit(1);
  }

  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const webhookUrl: string = await new Promise(resolve => {
    readline.question('Enter the full webhook URL for your Telegram bot (e.g., https://yourdomain.com/api/bot/webhook): ', resolve);
  });

  readline.close();

  if (!webhookUrl) {
    console.error('Error: Webhook URL cannot be empty.');
    process.exit(1);
  }

  console.log(`Attempting to set webhook to: ${webhookUrl}`);

  try {
    const response = await fetch('http://localhost:3000/api/bot/setup', { // Assuming local development server
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminApiToken}`
      },
      body: JSON.stringify({
        webhookUrl: webhookUrl,
        setCommands: true, // Optionally set to false if you don't want to set default commands
      }),
    });

    const data = await response.json();

    if (response.ok) {
      console.log('Bot setup successful!');
      console.log(JSON.stringify(data, null, 2));
    } else {
      console.error('Bot setup failed:', data.error || response.statusText);
      console.error(JSON.stringify(data, null, 2));
    }
  } catch (error: any) {
    console.error('An unexpected error occurred during bot setup:', error.message);
  }
}

setupTelegramBot();
