#!/usr/bin/env node

/**
 * Deploy Telegram Integration Script
 * 
 * This script ensures proper deployment of the Telegram bot integration
 * with Supabase Edge Functions and database triggers.
 * 
 * Features:
 * - Deploys Supabase Edge Functions
 * - Sets up database triggers
 * - Validates Telegram bot configuration
 * - Tests the complete flow
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n${colors.cyan}🔄 ${description}...${colors.reset}`);
  try {
    const output = execSync(command, { 
      stdio: 'pipe', 
      encoding: 'utf8',
      cwd: process.cwd()
    });
    log(`${colors.green}✅ ${description} completed successfully${colors.reset}`);
    return output;
  } catch (error) {
    log(`${colors.red}❌ ${description} failed:${colors.reset}`, 'red');
    log(error.message, 'red');
    throw error;
  }
}

function checkEnvironment() {
  log(`\n${colors.blue}🔍 Checking environment configuration...${colors.reset}`);
  
  const requiredEnvVars = [
    'TELEGRAM_BOT_TOKEN',
    'TELEGRAM_CHAT_IDS'
  ];
  
  const optionalEnvVars = [
    'NEXT_PUBLIC_GA_MEASUREMENT_ID'
  ];
  
  const missingVars = [];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  }
  
  if (missingVars.length > 0) {
    log(`${colors.red}❌ Missing required environment variables:${colors.reset}`, 'red');
    missingVars.forEach(varName => log(`  - ${varName}`, 'red'));
    log(`\n${colors.yellow}Please set these variables in your .env file or environment${colors.reset}`, 'yellow');
    process.exit(1);
  }
  
  // Check optional variables
  const missingOptionalVars = [];
  for (const envVar of optionalEnvVars) {
    if (!process.env[envVar]) {
      missingOptionalVars.push(envVar);
    }
  }
  
  if (missingOptionalVars.length > 0) {
    log(`${colors.yellow}⚠️ Optional environment variables not set:${colors.reset}`, 'yellow');
    missingOptionalVars.forEach(varName => log(`  - ${varName}`, 'yellow'));
    log(`${colors.cyan}ℹ️ Google Analytics will be disabled${colors.reset}`, 'cyan');
  }
  
  log(`${colors.green}✅ All required environment variables are set${colors.reset}`);
}

function validateTelegramConfig() {
  log(`\n${colors.blue}🤖 Validating Telegram bot configuration...${colors.reset}`);
  
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  const chatIds = process.env.TELEGRAM_CHAT_IDS?.split(',').map(id => id.trim()).filter(Boolean);
  
  if (!botToken || !chatIds || chatIds.length === 0) {
    log(`${colors.red}❌ Invalid Telegram configuration${colors.reset}`, 'red');
    process.exit(1);
  }
  
  // Validate bot token format
  if (!botToken.match(/^\d+:[A-Za-z0-9_-]+$/)) {
    log(`${colors.red}❌ Invalid bot token format${colors.reset}`, 'red');
    process.exit(1);
  }
  
  // Validate chat IDs
  for (const chatId of chatIds) {
    if (!chatId.match(/^-?\d+$/)) {
      log(`${colors.red}❌ Invalid chat ID format: ${chatId}${colors.reset}`, 'red');
      process.exit(1);
    }
  }
  
  log(`${colors.green}✅ Telegram bot configuration is valid${colors.reset}`);
  log(`  Bot Token: ${botToken.substring(0, 10)}...${botToken.substring(botToken.length - 10)}`);
  log(`  Chat IDs: ${chatIds.join(', ')}`);
}

function deploySupabaseFunctions() {
  log(`\n${colors.blue}🚀 Deploying Supabase Edge Functions...${colors.reset}`);
  
  try {
    // Check if Supabase CLI is installed
    execSync('supabase --version', { stdio: 'pipe' });
  } catch (error) {
    log(`${colors.red}❌ Supabase CLI not found. Please install it first:${colors.reset}`, 'red');
    log(`  npm install -g supabase`, 'yellow');
    process.exit(1);
  }
  
  // Deploy the send-telegram-notification function
  execCommand(
    'supabase functions deploy send-telegram-notification',
    'Deploying send-telegram-notification function'
  );
}

function setupDatabaseTriggers() {
  log(`\n${colors.blue}🗄️ Setting up database triggers...${colors.reset}`);
  
  const triggerSQL = `
-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION notify_telegram_on_application()
RETURNS TRIGGER AS $$
BEGIN
  -- Call the Edge Function to send Telegram notification
  PERFORM
    net.http_post(
      url := 'https://aqvztdxidpfirjvovhyi.supabase.co/functions/v1/send-telegram-notification',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key', true)
      ),
      body := jsonb_build_object(
        'record', row_to_json(NEW)
      )
    );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS job_application_insert_trigger ON job_applications;

-- Create the trigger
CREATE TRIGGER job_application_insert_trigger
  AFTER INSERT ON job_applications
  FOR EACH ROW
  EXECUTE FUNCTION notify_telegram_on_application();
`;

  // Write SQL to temporary file
  const tempSQLFile = path.join(__dirname, 'temp_trigger.sql');
  fs.writeFileSync(tempSQLFile, triggerSQL);
  
  try {
    // Try to apply the SQL directly without resetting the database
    log(`${colors.cyan}Applying database triggers without reset...${colors.reset}`);
    execCommand(
      `supabase db push --linked`,
      'Applying database changes'
    );
  } catch (error) {
    log(`${colors.yellow}⚠️ Could not apply database changes automatically${colors.reset}`, 'yellow');
    log(`${colors.cyan}ℹ️ You may need to manually apply the trigger SQL in your Supabase dashboard${colors.reset}`, 'cyan');
    log(`${colors.cyan}SQL file created at: ${tempSQLFile}${colors.reset}`, 'cyan');
  } finally {
    // Keep the SQL file for manual application if needed
    log(`${colors.cyan}📄 Trigger SQL saved to: ${tempSQLFile}${colors.reset}`, 'cyan');
  }
}

function testTelegramIntegration() {
  log(`\n${colors.blue}🧪 Testing Telegram integration...${colors.reset}`);
  
  const testPayload = {
    record: {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+966501234567',
      position: 'Test Position',
      experience: '3-5 years',
      cv_url: 'https://example.com/test-cv.pdf',
      language: 'en'
    }
  };
  
  try {
    const response = execSync(
      `curl -X POST "https://aqvztdxidpfirjvovhyi.supabase.co/functions/v1/send-telegram-notification" \\
        -H "Content-Type: application/json" \\
        -H "Authorization: Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key'}" \\
        -d '${JSON.stringify(testPayload)}'`,
      { stdio: 'pipe', encoding: 'utf8' }
    );
    
    const result = JSON.parse(response);
    if (result.success) {
      log(`${colors.green}✅ Telegram integration test successful${colors.reset}`);
      log(`  Sent to ${result.successCount}/${result.totalCount} chats`);
    } else {
      log(`${colors.red}❌ Telegram integration test failed${colors.reset}`, 'red');
      log(`  Error: ${result.error}`, 'red');
    }
  } catch (error) {
    log(`${colors.yellow}⚠️ Could not test Telegram integration (this is normal if service role key is not set)${colors.reset}`, 'yellow');
  }
}

function buildAndDeploy() {
  log(`\n${colors.blue}🏗️ Building and deploying application...${colors.reset}`);
  
  // Build the application
  execCommand('npm run build', 'Building Next.js application');
  
  // Copy .htaccess to out directory
  const htaccessSource = path.join(process.cwd(), '.htaccess');
  const htaccessDest = path.join(process.cwd(), 'out', '.htaccess');
  
  if (fs.existsSync(htaccessSource)) {
    fs.copyFileSync(htaccessSource, htaccessDest);
    log(`${colors.green}✅ Copied .htaccess to out directory${colors.reset}`);
  }
  
  log(`${colors.green}✅ Application built and ready for deployment${colors.reset}`);
  log(`  Build output: ${path.join(process.cwd(), 'out')}`);
}

function main() {
  log(`${colors.bright}${colors.cyan}🚀 Starting Telegram Integration Deployment${colors.reset}`);
  log(`${colors.cyan}================================================${colors.reset}`);
  
  try {
    checkEnvironment();
    validateTelegramConfig();
    deploySupabaseFunctions();
    setupDatabaseTriggers();
    testTelegramIntegration();
    buildAndDeploy();
    
    log(`\n${colors.bright}${colors.green}🎉 Deployment completed successfully!${colors.reset}`);
    log(`\n${colors.cyan}Next steps:${colors.reset}`);
    log(`1. Upload the contents of the 'out' directory to your web server`);
    log(`2. Ensure your .env file contains all required variables`);
    log(`3. Test the application form to verify Telegram notifications`);
    log(`4. Monitor the Supabase Edge Function logs for any issues`);
    
  } catch (error) {
    log(`\n${colors.red}❌ Deployment failed:${colors.reset}`, 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main };
