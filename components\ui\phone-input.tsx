'use client'

import { <PERSON>I<PERSON>, ChevronsUpDown } from 'lucide-react'
import * as React from 'react'
import { type Country, getCountryCallingCode } from 'react-phone-number-input'
import * as RPNInput from 'react-phone-number-input'

import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Input, type InputProps } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'

// --- Country Select
interface CountrySelectProps {
  value: Country
  onChange: (value: Country) => void
  disabled?: boolean
}

const CountrySelect: React.FC<CountrySelectProps> = ({ value, onChange, disabled }) => {
  const [open, setOpen] = React.useState(false)

  const handleSelect = React.useCallback((country: Country) => {
    onChange(country)
    setOpen(false)
  }, [onChange])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          className={cn('flex gap-2 rounded-e-none rounded-s-lg px-3')}
          disabled={disabled}
        >
          <span>{value}</span>
          <ChevronsUpDown className={cn('-mr-2 h-4 w-4', disabled ? 'hidden' : '')} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput placeholder="Search country..." />
          <CommandList>
            <ScrollArea className="h-auto max-h-[300px]">
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {RPNInput.getCountries().map((country) => (
                  <CommandItem key={country} onSelect={() => handleSelect(country)} value={country}>
                    <span className="flex-1 text-sm">{country} (+{RPNInput.getCountryCallingCode(country)})</span>
                    <CheckIcon
                      className={cn('ml-auto h-4 w-4', value === country ? 'opacity-100' : 'opacity-0')}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

// --- Phone Input
interface PhoneInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: string
  onChange: (value: string) => void
}

const PhoneInput = React.forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ className, value, onChange, ...props }, ref) => {
    const [country, setCountry] = React.useState<Country>('SA')

    const handleCountryChange = React.useCallback((newCountry: Country) => {
      const callingCode = getCountryCallingCode(newCountry)
      setCountry(newCountry)
      onChange(`+${callingCode}`)
    }, [onChange])

    const handlePhoneNumberChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      let { value: newValue } = event.target
      // Ensure the value always starts with the country's calling code
      const callingCode = getCountryCallingCode(country)
      if (!newValue.startsWith(`+${callingCode}`)) {
        newValue = `+${callingCode}`
      }
      onChange(newValue)
    }

    return (
      <div className={cn('flex', className)}>
        <CountrySelect value={country} onChange={handleCountryChange} />
        <Input
          ref={ref}
          type="tel"
          value={value}
          onChange={handlePhoneNumberChange}
          className="rounded-e-lg rounded-s-none"
          name="phone"
          {...props}
        />
      </div>
    )
  },
)
PhoneInput.displayName = 'PhoneInput'

export { PhoneInput }
