#!/usr/bin/env node

/**
 * Integration Test Script
 * 
 * Tests the complete flow from form submission to Telegram notifications
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testCSPConfiguration() {
  log(`\n${colors.blue}🔍 Testing CSP Configuration...${colors.reset}`);
  
  const htaccessPath = path.join(process.cwd(), '.htaccess');
  
  if (!fs.existsSync(htaccessPath)) {
    log(`${colors.red}❌ .htaccess file not found${colors.reset}`, 'red');
    return false;
  }
  
  const htaccessContent = fs.readFileSync(htaccessPath, 'utf8');
  
  const requiredDomains = [
    'https://www.google-analytics.com',
    'https://www.googletagmanager.com',
    'https://www.google-analytics.com/mp/collect',
    'https://analytics.google.com',
    'api.telegram.org'
  ];
  
  let allDomainsPresent = true;
  
  for (const domain of requiredDomains) {
    if (!htaccessContent.includes(domain)) {
      log(`${colors.red}❌ Missing domain in CSP: ${domain}${colors.reset}`, 'red');
      allDomainsPresent = false;
    }
  }
  
  if (allDomainsPresent) {
    log(`${colors.green}✅ CSP configuration includes all required domains${colors.reset}`);
    return true;
  }
  
  return false;
}

function testGoogleAnalyticsIntegration() {
  log(`\n${colors.blue}📊 Testing Google Analytics Integration...${colors.reset}`);
  
  const layoutPath = path.join(process.cwd(), 'app', 'layout.tsx');
  
  if (!fs.existsSync(layoutPath)) {
    log(`${colors.red}❌ layout.tsx file not found${colors.reset}`, 'red');
    return false;
  }
  
  const layoutContent = fs.readFileSync(layoutPath, 'utf8');
  
  const requiredElements = [
    'NEXT_PUBLIC_GA_MEASUREMENT_ID',
    'googletagmanager.com/gtag/js',
    'gtag(\'config\'',
    'window.dataLayer'
  ];
  
  let allElementsPresent = true;
  
  for (const element of requiredElements) {
    if (!layoutContent.includes(element)) {
      log(`${colors.red}❌ Missing GA element: ${element}${colors.reset}`, 'red');
      allElementsPresent = false;
    }
  }
  
  if (allElementsPresent) {
    log(`${colors.green}✅ Google Analytics integration is properly configured${colors.reset}`);
    return true;
  }
  
  return false;
}

function testTelegramIntegration() {
  log(`\n${colors.blue}🤖 Testing Telegram Integration...${colors.reset}`);
  
  const telegramFunctionPath = path.join(process.cwd(), 'supabase', 'functions', 'send-telegram-notification', 'index.ts');
  
  if (!fs.existsSync(telegramFunctionPath)) {
    log(`${colors.red}❌ Telegram Edge Function not found${colors.reset}`, 'red');
    return false;
  }
  
  const functionContent = fs.readFileSync(telegramFunctionPath, 'utf8');
  
  const requiredFeatures = [
    'sendTelegramMessage',
    'sendTelegramDocument',
    'maxRetries',
    'Rate limited',
    'Promise.allSettled',
    'isArabic',
    'Arabic'
  ];
  
  let allFeaturesPresent = true;
  
  for (const feature of requiredFeatures) {
    if (!functionContent.includes(feature)) {
      log(`${colors.red}❌ Missing Telegram feature: ${feature}${colors.reset}`, 'red');
      allFeaturesPresent = false;
    }
  }
  
  if (allFeaturesPresent) {
    log(`${colors.green}✅ Telegram integration includes all enhanced features${colors.reset}`);
    return true;
  }
  
  return false;
}

function testEnvironmentVariables() {
  log(`\n${colors.blue}🔧 Testing Environment Variables...${colors.reset}`);
  
  const envExamplePath = path.join(process.cwd(), 'env.example');
  
  if (!fs.existsSync(envExamplePath)) {
    log(`${colors.red}❌ env.example file not found${colors.reset}`, 'red');
    return false;
  }
  
  const envContent = fs.readFileSync(envExamplePath, 'utf8');
  
  const requiredVars = [
    'TELEGRAM_BOT_TOKEN',
    'TELEGRAM_CHAT_IDS'
  ];
  
  const optionalVars = [
    'NEXT_PUBLIC_GA_MEASUREMENT_ID'
  ];
  
  let allVarsPresent = true;
  
  for (const envVar of requiredVars) {
    if (!envContent.includes(envVar)) {
      log(`${colors.red}❌ Missing environment variable: ${envVar}${colors.reset}`, 'red');
      allVarsPresent = false;
    }
  }
  
  if (allVarsPresent) {
    log(`${colors.green}✅ All required environment variables are defined${colors.reset}`);
    
    // Check optional variables
    const missingOptionalVars = [];
    for (const envVar of optionalVars) {
      if (!envContent.includes(envVar)) {
        missingOptionalVars.push(envVar);
      }
    }
    
    if (missingOptionalVars.length > 0) {
      log(`${colors.yellow}⚠️ Optional variables not defined: ${missingOptionalVars.join(', ')}${colors.reset}`, 'yellow');
      log(`${colors.cyan}ℹ️ Google Analytics will be disabled if not set${colors.reset}`, 'cyan');
    }
    
    return true;
  }
  
  return false;
}

function testBuildProcess() {
  log(`\n${colors.blue}🏗️ Testing Build Process...${colors.reset}`);
  
  try {
    log(`${colors.cyan}Building application...${colors.reset}`);
    execSync('npm run build', { stdio: 'pipe' });
    log(`${colors.green}✅ Build completed successfully${colors.reset}`);
    
    // Check if out directory exists and has content
    const outDir = path.join(process.cwd(), 'out');
    if (fs.existsSync(outDir)) {
      const files = fs.readdirSync(outDir);
      if (files.length > 0) {
        log(`${colors.green}✅ Build output directory contains files${colors.reset}`);
        return true;
      }
    }
    
    log(`${colors.red}❌ Build output directory is empty${colors.reset}`, 'red');
    return false;
    
  } catch (error) {
    log(`${colors.red}❌ Build failed: ${error.message}${colors.reset}`, 'red');
    return false;
  }
}

function main() {
  log(`${colors.bright}${colors.cyan}🧪 Integration Test Suite${colors.reset}`);
  log(`${colors.cyan}========================${colors.reset}`);
  
  const tests = [
    { name: 'CSP Configuration', fn: testCSPConfiguration },
    { name: 'Google Analytics Integration', fn: testGoogleAnalyticsIntegration },
    { name: 'Telegram Integration', fn: testTelegramIntegration },
    { name: 'Environment Variables', fn: testEnvironmentVariables },
    { name: 'Build Process', fn: testBuildProcess }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      if (test.fn()) {
        passedTests++;
      }
    } catch (error) {
      log(`${colors.red}❌ ${test.name} test failed with error: ${error.message}${colors.reset}`, 'red');
    }
  }
  
  log(`\n${colors.cyan}📊 Test Results:${colors.reset}`);
  log(`${colors.green}✅ Passed: ${passedTests}/${totalTests}${colors.reset}`);
  
  if (passedTests === totalTests) {
    log(`\n${colors.bright}${colors.green}🎉 All tests passed! Integration is ready for deployment.${colors.reset}`);
    log(`\n${colors.cyan}Next steps:${colors.reset}`);
    log(`1. Set your environment variables in .env file`);
    log(`2. Deploy using: node scripts/deploy-telegram-integration.js`);
    log(`3. Upload the 'out' directory to your web server`);
    log(`4. Test the application form submission`);
  } else {
    log(`\n${colors.red}❌ Some tests failed. Please fix the issues before deployment.${colors.reset}`);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { main };