/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimized for Hostinger Premium Static Hosting (Sep 2025)
  output: 'export',
  trailingSlash: true,
  distDir: 'out',
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
      // Optimize for static hosting and performance
      compress: true,
      poweredByHeader: false,
      generateEtags: false,
      compiler: {
        removeConsole: process.env.NODE_ENV === 'production' ? {
          exclude: ['error', 'warn']
        } : false,
      },
      experimental: {
        webVitalsAttribution: ['CLS', 'LCP'],
      },
  // Disable image optimization for static export
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'aqvztdxidpfirjvovhyi.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
  // Disable React Refresh to fix EvalError
  reactStrictMode: false, // Disable React Strict Mode
  onDemandEntries: {
    maxInactiveAge: 60 * 60 * 1000,
    pagesBufferLength: 2,
  },
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      // Fix development cache conflicts with out directory
      config.cache = {
        type: 'filesystem',
        cacheDirectory: '.next/cache/webpack',
        buildDependencies: {
          config: ['next.config.mjs'],
        },
      };
      
      // Completely remove React Refresh
      config.plugins = config.plugins.filter(
        (plugin) => plugin.constructor.name !== 'ReactRefreshWebpackPlugin'
      );
      
      // Remove React Refresh loader from all rules
      const reactRefreshRules = [];
      config.module.rules.forEach((rule) => {
        if (rule.use && Array.isArray(rule.use)) {
          const filteredUse = rule.use.filter(
            (use) => !(use.loader && use.loader.includes('react-refresh'))
          );
          
          if (filteredUse.length > 0) {
            reactRefreshRules.push({
              ...rule,
              use: filteredUse,
            });
          }
        } else {
          reactRefreshRules.push(rule);
        }
      });
      
      config.module.rules = reactRefreshRules;
      
      // Also remove React Refresh from fallback module rules
      if (config.module.rules[0] && config.module.rules[0].oneOf) {
        config.module.rules[0].oneOf = config.module.rules[0].oneOf.map((one) => {
          if (one.use && Array.isArray(one.use)) {
            return {
              ...one,
              use: one.use.filter(
                (use) => !(use.loader && use.loader.includes('react-refresh'))
              ),
            };
          }
          return one;
        });
      }
    }
    
    return config;
  },
  // Note: Headers are not supported with static export
  // Headers will be configured in .htaccess for Hostinger
}

export default nextConfig
