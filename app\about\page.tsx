"use client"

import { useLanguage } from "@/lib/language-context"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Target, 
  Users, 
  Award, 
  Globe, 
  TrendingUp, 
  Heart 
} from "lucide-react"
import Image from "next/image"

export default function AboutPage() {
  const { language, isRTL } = useLanguage()

  const values = [
    {
      icon: Target,
      title: language === "ar" ? "التميز" : "Excellence",
      description: language === "ar" 
        ? "نسعى لتحقيق أعلى معايير الجودة في كل ما نقوم به" 
        : "We strive to achieve the highest quality standards in everything we do"
    },
    {
      icon: Users,
      title: language === "ar" ? "الشراكة" : "Partnership",
      description: language === "ar" 
        ? "نعتبر عملاءنا ومرشدينا شركاء في النجاح" 
        : "We consider our clients and candidates partners in success"
    },
    {
      icon: Award,
      title: language === "ar" ? "النزاهة" : "Integrity",
      description: language === "ar" 
        ? "نلتزم بأعلى المعايير الأخلاقية في جميع تعاملاتنا" 
        : "We commit to the highest ethical standards in all our dealings"
    },
    {
      icon: Heart,
      title: language === "ar" ? "الاحترام" : "Respect",
      description: language === "ar" 
        ? "نعامل جميع الأطراف بكرامة واحترام متبادل" 
        : "We treat all parties with dignity and mutual respect"
    },
    {
      icon: TrendingUp,
      title: language === "ar" ? "الابتكار" : "Innovation",
      description: language === "ar" 
        ? "نحافظ على مقدمة الريادة من خلال الحلول الإبداعية" 
        : "We stay ahead through creative solutions"
    },
    {
      icon: Globe,
      title: language === "ar" ? "المسؤولية" : "Responsibility",
      description: language === "ar" 
        ? "نتحمل مسؤولية تأثيرنا على المجتمع والبيئة" 
        : "We take responsibility for our impact on society and the environment"
    }
  ]

  const timeline = [
    {
      year: "2020",
      title: language === "ar" ? "تأسيس سكيلزا" : "Skillza Founded",
      description: language === "ar" 
        ? "تم تأسيس سكيلزا بهدف تحويل مشهد التوظيف في المملكة العربية السعودية" 
        : "Skillza was founded with the goal of transforming the recruitment landscape in Saudi Arabia"
    },
    {
      year: "2021",
      title: language === "ar" ? "التوسع الأول" : "First Expansion",
      description: language === "ar" 
        ? "توسعنا لتشمل خدمات التوظيف في مختلف القطاعات الرئيسية" 
        : "We expanded to include recruitment services across major sectors"
    },
    {
      year: "2022",
      title: language === "ar" ? "الشراكات الاستراتيجية" : "Strategic Partnerships",
      description: language === "ar" 
        ? " أبرمنا شراكات استراتيجية مع كبرى الشركات في المملكة" 
        : "We established strategic partnerships with leading companies in the Kingdom"
    },
    {
      year: "2023",
      title: language === "ar" ? "الاعتراف الوطني" : "National Recognition",
      description: language === "ar" 
        ? "تم الاعتراف بنا كواحدة من أفضل شركات التوظيف في المملكة" 
        : "We were recognized as one of the top recruitment companies in the Kingdom"
    },
    {
      year: "2024",
      title: language === "ar" ? "التوسع الرقمي" : "Digital Expansion",
      description: language === "ar" 
        ? "قمنا بتطوير منصتنا الرقمية لتقديم تجربة توظيف أفضل" 
        : "We developed our digital platform to provide a better recruitment experience"
    },
    {
      year: "2025",
      title: language === "ar" ? "الرؤية المستقبلية" : "Future Vision",
      description: language === "ar" 
        ? "نسعى لتصبح منصة التوظيف الرائدة في المنطقة" 
        : "We aim to become the leading recruitment platform in the region"
    }
  ]

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow pt-24 pb-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <span className={`inline-block px-3 py-1 rounded-full bg-indigo-100 text-indigo-800 text-sm font-medium mb-4 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "عن سكيلزا" : "About Skillza"}
          </span>
          <h1 className={`text-4xl lg:text-5xl font-bold text-slate-900 mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "塑造未来人才" : "Shaping Future Talent"}
          </h1>
          <p className={`text-xl text-slate-600 max-w-3xl mx-auto ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "سكيلزا هي شركة رائدة في مجال التوظيف والموارد البشرية، متخصصة في ربط المواهب المتميزة بأفضل الفرص الوظيفية في المملكة العربية السعودية." 
              : "Skillza is a leading recruitment and human resources company, specializing in connecting exceptional talent with the best job opportunities in Saudi Arabia."}
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50">
            <CardContent className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="bg-indigo-100 p-3 rounded-lg">
                  <Target className="text-indigo-600" size={24} />
                </div>
                <h2 className={`text-2xl font-bold text-slate-900 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {language === "ar" ? "مهمتنا" : "Our Mission"}
                </h2>
              </div>
              <p className={`text-slate-600 text-lg leading-relaxed ${isRTL ? "font-arabic" : "font-sans"}`}>
                {language === "ar" 
                  ? "نسعى لتوفير حلول توظيف شاملة وفعالة تربط بين أفضل المواهب والشركات الرائدة في المملكة العربية السعودية، مع التركيز على الجودة والاحترافية والابتكار في جميع خدماتنا." 
                  : "We strive to provide comprehensive and effective recruitment solutions that connect top talent with leading companies in Saudi Arabia, focusing on quality, professionalism, and innovation in all our services."}
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50">
            <CardContent className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="bg-indigo-100 p-3 rounded-lg">
                  <Globe className="text-indigo-600" size={24} />
                </div>
                <h2 className={`text-2xl font-bold text-slate-900 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {language === "ar" ? "رؤيتنا" : "Our Vision"}
                </h2>
              </div>
              <p className={`text-slate-600 text-lg leading-relaxed ${isRTL ? "font-arabic" : "font-sans"}`}>
                {language === "ar" 
                  ? "أن نصبح الشريك المفضل في مجال التوظيف والموارد البشرية في المملكة العربية السعودية، ونساهم في بناء مستقبل مزدهر من خلال تمكين الأفراد والشركات لتحقيق أهدافهم المهنية." 
                  : "To become the preferred partner in recruitment and human resources in Saudi Arabia, contributing to a prosperous future by empowering individuals and companies to achieve their professional goals."}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="mb-16">
          <h2 className={`text-3xl font-bold text-slate-900 mb-4 text-center ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "قيمنا الأساسية" : "Our Core Values"}
          </h2>
          <p className={`text-slate-600 text-center max-w-3xl mx-auto mb-12 text-lg ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "تقوم ثقافتنا المؤسسية على مجموعة من القيم التي توجه كل ما نقوم به" 
              : "Our corporate culture is built on a set of values that guide everything we do"}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all">
                  <CardContent className="p-6">
                    <div className="bg-indigo-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                      <IconComponent className="text-indigo-600" size={24} />
                    </div>
                    <h3 className={`font-bold text-xl text-slate-900 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}>
                      {value.title}
                    </h3>
                    <p className={`text-slate-600 ${isRTL ? "font-arabic" : "font-sans"}`}>
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Company History */}
        <div className="mb-16">
          <h2 className={`text-3xl font-bold text-slate-900 mb-4 text-center ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "رحلتنا" : "Our Journey"}
          </h2>
          <p className={`text-slate-600 text-center max-w-3xl mx-auto mb-12 text-lg ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "منذ تأسيسنا، نعمل على تمكين الأفراد والشركات لتحقيق أهدافهم المهنية" 
              : "Since our founding, we've been empowering individuals and companies to achieve their professional goals"}
          </p>
          
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-indigo-200 hidden lg:block"></div>
            
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div 
                  key={index} 
                  className={`flex flex-col lg:flex-row items-center ${index % 2 === 0 ? 'lg:flex-row-reverse' : ''} gap-8`}
                >
                  <div className="lg:w-1/2 lg:px-8">
                    <div className={`bg-indigo-100 text-indigo-800 font-bold text-lg w-16 h-16 rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4 ${isRTL ? "font-arabic" : "font-sans"}`}>
                      {item.year}
                    </div>
                    <h3 className={`text-xl font-bold text-slate-900 mb-2 text-center lg:text-left ${isRTL ? "font-arabic" : "font-sans"}`}>
                      {item.title}
                    </h3>
                    <p className={`text-slate-600 text-center lg:text-left ${isRTL ? "font-arabic" : "font-sans"}`}>
                      {item.description}
                    </p>
                  </div>
                  
                  <div className="lg:w-1/2 flex justify-center">
                    <div className="bg-slate-200 border-2 border-dashed rounded-xl w-16 h-16 lg:w-32 lg:h-32" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="text-center">
          <h2 className={`text-3xl font-bold text-slate-900 mb-4 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "فريق الخبراء لدينا" : "Our Expert Team"}
          </h2>
          <p className={`text-slate-600 max-w-3xl mx-auto mb-12 text-lg ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "فريق متخصص من مستشاري التوظيف والموارد البشرية الذين يمتلكون خبرة واسعة في مختلف القطاعات" 
              : "A specialized team of recruitment and HR consultants with extensive experience across various sectors"}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[1, 2, 3].map((item) => (
              <Card key={item} className="border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="bg-slate-200 border-2 border-dashed rounded-xl w-32 h-32 mx-auto mb-4" />
                  <h3 className={`font-bold text-xl text-slate-900 mb-1 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    {language === "ar" ? "مدير التوظيف" : "Recruitment Director"}
                  </h3>
                  <p className={`text-indigo-600 mb-3 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    {language === "ar" ? "الموارد البشرية" : "Human Resources"}
                  </p>
                  <p className={`text-slate-600 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    {language === "ar" 
                      ? "يملك أكثر من 10 سنوات خبرة في مجال التوظيف والاستقطاب" 
                      : "Over 10 years of experience in recruitment and talent acquisition"}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
      </main>
      <Footer />
    </div>
  )
}
