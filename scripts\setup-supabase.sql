-- Supabase Database Setup Script
-- Run this in your Supabase SQL editor

-- Create jobs table
CREATE TABLE IF NOT EXISTS jobs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title JSONB NOT NULL, -- {ar: string, en: string}
  sector TEXT NOT NULL,
  location JSONB NOT NULL, -- {ar: string, en: string}
  description JSONB NOT NULL, -- {ar: string, en: string}
  requirements JSONB NOT NULL, -- {ar: string[], en: string[]}
  posted_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  type JSONB NOT NULL, -- {ar: string, en: string}
  salary JSONB NOT NULL, -- {ar: string, en: string}
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT NOT NULL,
  experience TEXT NOT NULL,
  position TEXT NOT NULL,
  cv_url TEXT NOT NULL,
  consent BOOLEAN NOT NULL DEFAULT false,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE applications ENABLE ROW LEVEL SECURITY;

-- Create policies for jobs table
CREATE POLICY "Jobs are viewable by everyone" ON jobs
  FOR SELECT USING (true);

-- Create policies for applications table
CREATE POLICY "Anyone can insert applications" ON applications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Applications are viewable by service role" ON applications
  FOR SELECT USING (auth.role() = 'service_role');

-- Create storage bucket for applications
INSERT INTO storage.buckets (id, name, public)
VALUES ('applications', 'applications', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies
CREATE POLICY "Anyone can upload to applications bucket" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'applications');

CREATE POLICY "Anyone can view files in applications bucket" ON storage.objects
  FOR SELECT USING (bucket_id = 'applications');

-- Insert sample job data
INSERT INTO jobs (title, sector, location, description, requirements, type, salary) VALUES
(
  '{"ar": "مطور ويب", "en": "Web Developer"}',
  'Technology',
  '{"ar": "الرياض، المملكة العربية السعودية", "en": "Riyadh, Saudi Arabia"}',
  '{"ar": "نبحث عن مطور ويب مبدع ومتحمس للانضمام إلى فريقنا.", "en": "We are looking for a creative and passionate web developer to join our team."}',
  '{"ar": ["خبرة 3+ سنوات في تطوير الويب", "معرفة بـ React و Node.js", "خبرة في قواعد البيانات"], "en": ["3+ years experience in web development", "Knowledge of React and Node.js", "Database experience"]}',
  '{"ar": "دوام كامل", "en": "Full-time"}',
  '{"ar": "8,000 - 12,000 ريال", "en": "8,000 - 12,000 SAR"}'
),
(
  '{"ar": "مصمم جرافيك", "en": "Graphic Designer"}',
  'Design',
  '{"ar": "جدة، المملكة العربية السعودية", "en": "Jeddah, Saudi Arabia"}',
  '{"ar": "نبحث عن مصمم جرافيك موهوب لإنشاء محتوى بصري مذهل.", "en": "We are looking for a talented graphic designer to create stunning visual content."}',
  '{"ar": ["خبرة 2+ سنوات في التصميم الجرافيكي", "إتقان Adobe Creative Suite", "محفظة أعمال قوية"], "en": ["2+ years experience in graphic design", "Proficiency in Adobe Creative Suite", "Strong portfolio"]}',
  '{"ar": "دوام جزئي", "en": "Part-time"}',
  '{"ar": "4,000 - 6,000 ريال", "en": "4,000 - 6,000 SAR"}'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
CREATE INDEX IF NOT EXISTS idx_jobs_posted_date ON jobs(posted_date);
