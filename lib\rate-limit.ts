interface RateLimitEntry {
  count: number
  resetTime: number
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  retryAfter?: number
}

class EnhancedRateLimiter {
  private store = new Map<string, RateLimitEntry>()
  private readonly maxRequests: number
  private readonly windowMs: number
  private readonly keyPrefix: string

  constructor(maxRequests = 1, windowMs = 60000, keyPrefix = 'rate') {
    this.maxRequests = maxRequests
    this.windowMs = windowMs
    this.keyPrefix = keyPrefix
  }

  check(identifier: string): RateLimitResult {
    const key = `${this.keyPrefix}:${identifier}`
    const now = Date.now()
    const entry = this.store.get(key)

    if (!entry || now > entry.resetTime) {
      // First request or window has reset
      const newEntry = {
        count: 1,
        resetTime: now + this.windowMs,
      }
      this.store.set(key, newEntry)
      return {
        allowed: true,
        remaining: this.maxRequests - 1,
        resetTime: newEntry.resetTime,
      }
    }

    if (entry.count >= this.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil((entry.resetTime - now) / 1000),
      }
    }

    entry.count++
    return {
      allowed: true,
      remaining: this.maxRequests - entry.count,
      resetTime: entry.resetTime,
    }
  }

  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key)
      }
    }
  }

  // Get current status without incrementing
  status(identifier: string): RateLimitResult {
    const key = `${this.keyPrefix}:${identifier}`
    const now = Date.now()
    const entry = this.store.get(key)

    if (!entry || now > entry.resetTime) {
      return {
        allowed: true,
        remaining: this.maxRequests,
        resetTime: now + this.windowMs,
      }
    }

    const remaining = Math.max(0, this.maxRequests - entry.count)
    return {
      allowed: remaining > 0,
      remaining,
      resetTime: entry.resetTime,
      retryAfter: remaining === 0 ? Math.ceil((entry.resetTime - now) / 1000) : undefined,
    }
  }
}

// Multiple rate limiters for different aspects
export const ipRateLimit = new EnhancedRateLimiter(3, 60000, 'ip') // 3 requests per minute per IP
export const emailRateLimit = new EnhancedRateLimiter(1, 300000, 'email') // 1 request per 5 minutes per email
export const globalRateLimit = new EnhancedRateLimiter(10, 60000, 'global') // 10 requests per minute globally

// Cleanup expired entries every 5 minutes
setInterval(
  () => {
    ipRateLimit.cleanup()
    emailRateLimit.cleanup()
    globalRateLimit.cleanup()
  },
  5 * 60 * 1000,
)
