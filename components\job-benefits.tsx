"use client"

import { useLanguage } from "@/lib/language-context"
import { Card, CardContent } from "@/components/ui/card"
import { 
  DollarSign, 
  Clock, 
  Users, 
  Award, 
  Building, 
  Shield, 
  Globe 
} from "lucide-react"
import Image from "next/image"

export function JobBenefits() {
  const { language, isRTL } = useLanguage()

  const benefits = [
    {
      icon: DollarSign,
      title: language === "ar" ? "راتب تنافسي" : "Competitive Salary",
      description: language === "ar" 
        ? "نوفر رواتب تنافسية تتماشى مع معايير السوق المحلي" 
        : "We offer competitive salaries aligned with local market standards"
    },
    {
      icon: Clock,
      title: language === "ar" ? "توازن العمل والحياة" : "Work-Life Balance",
      description: language === "ar" 
        ? "ساعات عمل مرنة وبيئة عمل تدعم التوازن بين العمل والحياة الشخصية" 
        : "Flexible working hours and an environment that supports work-life balance"
    },
    {
      icon: Users,
      title: language === "ar" ? "فريق داعم" : "Supportive Team",
      description: language === "ar" 
        ? "انضم إلى فريق متعاون وداعم في بيئة عمل إيجابية" 
        : "Join a collaborative and supportive team in a positive work environment"
    },
    {
      icon: Award,
      title: language === "ar" ? "فرص للنمو" : "Growth Opportunities",
      description: language === "ar" 
        ? "خطط تدريب وتطوير مهني للتقدم الوظيفي" 
        : "Training and professional development plans for career advancement"
    },
    {
      icon: Building,
      title: language === "ar" ? "بيئة عمل حديثة" : "Modern Work Environment",
      description: language === "ar" 
        ? "مرافق حديثة ومجهزة بأحدث التقنيات" 
        : "Modern facilities equipped with the latest technologies"
    },
    {
      icon: Shield,
      title: language === "ar" ? "تأمين صحي شامل" : "Comprehensive Health Insurance",
      description: language === "ar" 
        ? "تأمين طبي شامل لموظفيينا وعائلاتهم" 
        : "Comprehensive medical insurance for our employees and their families"
    }
  ]

  const opportunities = [
    {
      title: language === "ar" ? "التدريب المستمر" : "Continuous Training",
      description: language === "ar" 
        ? "برامج تدريبية منتظمة لتطوير المهارات التقنية والإدارية" 
        : "Regular training programs to develop technical and managerial skills"
    },
    {
      title: language === "ar" ? "التقدم الوظيفي" : "Career Advancement",
      description: language === "ar" 
        ? "مسارات واضحة للترقي والنمو الوظيفي" 
        : "Clear paths for promotion and career growth"
    },
    {
      title: language === "ar" ? "المشاريع المثيرة" : "Exciting Projects",
      description: language === "ar" 
        ? "فرص للعمل على مشاريع مبتكرة ومؤثرة" 
        : "Opportunities to work on innovative and impactful projects"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-white to-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className={`inline-block px-3 py-1 rounded-full bg-indigo-100 text-indigo-800 text-sm font-medium mb-4 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "المزايا والفرص" : "Benefits & Opportunities"}
          </span>
          <h2 className={`text-3xl lg:text-5xl font-bold text-slate-900 mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "ما نقدمه لموظفيينا" : "What We Offer Our Employees"}
          </h2>
          <p className={`text-xl text-slate-600 max-w-3xl mx-auto ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "نؤمن بأن موظفيينا هم أعظم أصولنا، ولهذا نوفر بيئة عمل مثالية لمساعدتهم على النمو والازدهار" 
              : "We believe our employees are our greatest assets, which is why we provide an ideal work environment to help them grow and thrive"}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {benefits.map((benefit, index) => {
            // Define image paths for each benefit
            const imagePaths = [
              "/images/benefits/competitive-salary.jpg",
              "/images/benefits/work-life-balance.jpg",
              "/images/benefits/supportive-team.jpg",
              "/images/benefits/growth-opportunities.jpg",
              "/images/benefits/modern-workplace.jpg",
              "/images/benefits/health-insurance.jpg"
            ];
            
            // Fallback images from Unsplash
            const fallbackImages = [
              "https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?q=80&w=1471&auto=format&fit=crop",
              "https://images.unsplash.com/photo-1600880292089-90a7e086ee0c?q=80&w=1470&auto=format&fit=crop",
              "https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=1470&auto=format&fit=crop",
              "https://images.unsplash.com/photo-1507679799987-c73779587ccf?q=80&w=1471&auto=format&fit=crop",
              "https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1469&auto=format&fit=crop",
              "https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&w=1470&auto=format&fit=crop"
            ];
            
            return (
              <Card 
                key={index} 
                className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white overflow-hidden group hover:scale-105"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image 
                    src={fallbackImages[index]} 
                    alt={benefit.title}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    style={{ objectFit: "cover" }}
                    className="transition-transform duration-500 group-hover:scale-110"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = fallbackImages[index];
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent"></div>
                  <h3 className={`absolute bottom-4 left-4 right-4 font-bold text-lg text-white ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                    {benefit.title}
                  </h3>
                </div>
                <CardContent className="p-6">
                  <p className={`text-slate-600 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-3xl p-8 md:p-12 border border-indigo-100">
          <h3 className={`text-2xl lg:text-3xl font-bold text-slate-900 mb-8 text-center ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? "فرص النمو المهني" : "Professional Growth Opportunities"}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {opportunities.map((opportunity, index) => {
              // Different icons for each opportunity
              const icons = [Clock, Award, Globe];
              const IconComponent = icons[index] || Globe;
              
              return (
                <div 
                  key={index} 
                  className={`text-center group ${isRTL ? "text-right" : "text-left"}`}
                >
                  <div className="bg-white w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-md transition-all duration-300 group-hover:shadow-lg group-hover:scale-105">
                    <IconComponent className="text-indigo-600" size={32} />
                  </div>
                  <h4 className={`font-bold text-xl text-slate-900 mb-3 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    {opportunity.title}
                  </h4>
                  <p className={`text-slate-600 ${isRTL ? "font-arabic" : "font-sans"}`}>
                    {opportunity.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  )
}