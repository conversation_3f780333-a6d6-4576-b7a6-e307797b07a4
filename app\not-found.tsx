import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Home, ArrowLeft } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white flex items-center justify-center px-4">
      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-4">
          <div className="text-6xl font-bold text-indigo-600 mb-4">404</div>
          <CardTitle className="text-2xl font-bold text-slate-900 font-arabic">
            الصفحة غير موجودة
          </CardTitle>
          <p className="text-slate-600 font-arabic">
            عذراً، الصفحة التي تبحث عنها غير موجودة
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <p className="text-slate-600 mb-6 font-arabic">
              قد تكون الصفحة قد تم نقلها أو حذفها، أو ربما أخطأت في كتابة الرابط
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button asChild className="bg-indigo-600 hover:bg-indigo-700">
                <Link href="/" className="flex items-center gap-2">
                  <Home className="w-4 h-4" />
                  الصفحة الرئيسية
                </Link>
              </Button>
              <Button asChild variant="outline" className="border-slate-300">
                <Link href="/jobs" className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  الوظائف المتاحة
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
