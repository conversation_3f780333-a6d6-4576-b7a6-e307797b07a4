# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/
/.dist
/.qwen

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Logs
logs
*.log

# IDEs and editors
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# test coverage
coverage/
*.lcov

# misc
*.swp
*.swo

# local env
.env.local
.env.development.local
.env.test.local
.env.production.local

# npm
package-lock.json
npm-debug.log

# yarn
yarn.lock
yarn-error.log

# pnpm
pnpm-lock.yaml
.trae/
.gemini/
CREDENTIALS.md
credentials.yaml
DEPLOYMENT_FIXES.md
DEPLOYMENT_CHECKLIST.md
DEPLOYMENT.md
GEMINI.md
GEMINI.md
HOSTINGER_COMPARISON.md
HOSTINGER_STATIC_DEPLOYMENT.md
static.dist
dist-static
HOSTINGER_SETUP.md
context7.json
docs/JOB_DATE_UPDATES.md
docs/SUPABASE_MCP_INTEGRATION.md
scripts/crontab.example
.md/
