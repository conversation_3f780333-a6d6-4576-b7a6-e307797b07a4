"use client"

import Link from "next/link"
import { useLanguage } from "@/lib/language-context"
import { Button } from "@/components/ui/button"
import { 
  Mail, 
  Phone, 
  MapPin
} from "lucide-react"

export function Footer() {
  const { language, isRTL } = useLanguage()

  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Company Info */}
          <div className={`lg:col-span-1 ${isRTL ? "text-right" : "text-left"}`}>
            <div className="flex items-center gap-3 mb-6">
              <div className="bg-indigo-600 text-white font-bold text-xl w-12 h-12 rounded-xl flex items-center justify-center shadow-lg">
                S
              </div>
              <span className={`text-2xl font-bold ${isRTL ? "font-arabic" : "font-sans"}`}>
                <PERSON>ll<PERSON>
              </span>
            </div>
            <p className={`text-slate-400 mb-6 max-w-xs ${isRTL ? "font-arabic" : "font-sans"}`}>
              {language === "ar" 
                ? "شريكك المتخصص في التوظيف والموارد البشرية في المملكة العربية السعودية"
                : "Your specialized partner in recruitment and human resources in Saudi Arabia"}
            </p>
          </div>

          {/* Quick Links */}
          <div className={isRTL ? "text-right" : "text-left"}>
            <h3 className={`text-lg font-semibold mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}>
              {language === "ar" ? "روابط سريعة" : "Quick Links"}
            </h3>
            <ul className="space-y-4">
              <li>
                <Link 
                  href="/about" 
                  className={`text-slate-400 hover:text-white transition-colors ${isRTL ? "font-arabic" : "font-sans"}`}
                >
                  {language === "ar" ? "من نحن" : "About Us"}
                </Link>
              </li>
              <li>
                <Link 
                  href="/jobs" 
                  className={`text-slate-400 hover:text-white transition-colors ${isRTL ? "font-arabic" : "font-sans"}`}
                >
                  {language === "ar" ? "الوظائف" : "Jobs"}
                </Link>
              </li>
              <li>
                <Link 
                  href="/contact" 
                  className={`text-slate-400 hover:text-white transition-colors ${isRTL ? "font-arabic" : "font-sans"}`}
                >
                  {language === "ar" ? "اتصل بنا" : "Contact Us"}
                </Link>
              </li>
              <li>
                <Link 
                  href="/apply" 
                  className={`text-slate-400 hover:text-white transition-colors ${isRTL ? "font-arabic" : "font-sans"}`}
                >
                  {language === "ar" ? "تقدم الآن" : "Apply Now"}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className={isRTL ? "text-right" : "text-left"}>
            <h3 className={`text-lg font-semibold mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}>
              {language === "ar" ? "تواصل معنا" : "Contact Us"}
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start gap-3">
                <MapPin size={18} className="text-slate-400 mt-1 flex-shrink-0" />
                <span className={`text-slate-400 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {language === "ar" 
                    ? "الرياض، المملكة العربية السعودية" 
                    : "Riyadh, Saudi Arabia"}
                </span>
              </li>
              <li className="flex items-center gap-3">
                <Phone size={18} className="text-slate-400 flex-shrink-0" />
                <span className={`text-slate-400 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  +966 11 123 4567
                </span>
              </li>
              <li className="flex items-center gap-3">
                <Mail size={18} className="text-slate-400 flex-shrink-0" />
                <span className={`text-slate-400 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  <EMAIL>
                </span>
              </li>
            </ul>
            
            <div className="mt-6">
              <Link href="/apply">
                <Button 
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg transition-all hover:scale-105"
                >
                  {language === "ar" ? "تقدم الآن" : "Apply Now"}
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-800 mt-16 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className={`text-slate-300 text-sm ${isRTL ? "font-arabic" : "font-sans"}`}>
            &copy; {new Date().getFullYear()} Skillza. {language === "ar" ? "جميع الحقوق محفوظة" : "All rights reserved"}
          </p>
        </div>
      </div>
    </footer>
  )
}