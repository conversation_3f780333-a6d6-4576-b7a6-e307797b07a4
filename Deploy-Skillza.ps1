# =================================================================
# Skillza - Complete Deployment Script for Windows 11 PowerShell
# Enhanced Telegram Bot Integration + Hostinger Static Deployment
# Compatible with Supabase CLI, Next.js 15.5.3, Sep 2025 Standards
# =================================================================

param(
    [Parameter(Mandatory = $false)]
    [ValidateSet("check", "setup", "build", "deploy", "test", "full")]
    [string]$Mode = "full",
    
    [Parameter(Mandatory = $false)]
    [string]$SupabaseAccessToken,
    
    [Parameter(Mandatory = $false)]
    [string]$TelegramBotToken,
    
    [Parameter(Mandatory = $false)]
    [string[]]$TelegramChatIds,
    
    [Parameter(Mandatory = $false)]
    [string]$ProjectRef = "qvztdxidpfirjvovhyi",
    [Parameter(Mandatory = $false)]
    [switch]$Force = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$DryRun = $false
)

# =================================================================
# Global Configuration & Colors
# =================================================================
$ErrorActionPreference = "Stop"
$ProgressPreference = "Continue"

# ANSI Colors for Windows Terminal/PowerShell 7+
$Colors = @{
    Reset = "`e[0m"
    Bold = "`e[1m"
    Green = "`e[32m"
    Yellow = "`e[33m"
    Red = "`e[31m"
    Blue = "`e[34m"
    Cyan = "`e[36m"
    Magenta = "`e[35m"
    White = "`e[97m"
}

# Project Paths
$ProjectRoot = (Get-Location).Path
$SupabaseDir = Join-Path $ProjectRoot "supabase"
$ScriptsDir = Join-Path $ProjectRoot "scripts"
$DistStaticDir = Join-Path $ProjectRoot "dist-static"
$FunctionsDir = Join-Path $SupabaseDir "functions"
$EdgeFunctionDir = Join-Path $FunctionsDir "send-telegram-notification"
$MigrationsDir = Join-Path $SupabaseDir "migrations"
$BuildScript = Join-Path $ScriptsDir "build-static-hostinger.js"
$HtaccessPath = Join-Path $ProjectRoot ".htaccess"
$DeployScript = Join-Path $ScriptsDir "deploy-simple.js"

# Environment Variables
$env:PNPM_HOME = Join-Path $env:LOCALAPPDATA "pnpm"
$env:PATH += ";$env:PNPM_HOME"

# Log function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "Cyan"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $colorCode = $Colors[$Color]
    $levelColor = switch ($Level) {
        "ERROR" { $Colors.Red }
        "WARN" { $Colors.Yellow }
        "SUCCESS" { $Colors.Green }
        "INFO" { $Colors.Cyan }
        default { $Colors.Cyan }
    }
    Write-Host "${colorCode}[${timestamp}] ${levelColor}${Level}: ${Message}${Colors.Reset}" -NoNewline
}

function Write-Success { Write-Log $args "SUCCESS" "Green" }
function Write-Info { Write-Log $args "INFO" "Cyan" }
function Write-Warn { Write-Log $args "WARN" "Yellow" }
function Write-ErrorLog { Write-Log $args "ERROR" "Red" }

# =================================================================
# Function: Check Prerequisites
# =================================================================
function Test-Prerequisites {
    Write-Info "🔍 Checking prerequisites..."
    
    # Check PowerShell version (Windows 11 requires 5.1+)
    $psVersion = $PSVersionTable.PSVersion.Major
    if ($psVersion -lt 5) {
        Write-ErrorLog "PowerShell version $psVersion detected. Windows 11 requires PowerShell 5.1 or higher."
        exit 1
    }
    
    # Check Node.js (18+ for Next.js 15)
    try {
        $nodeVersion = node --version 2>$null
        if (-not $nodeVersion -or [version]($nodeVersion -replace 'v', '') -lt [version]"18.0.0") {
            Write-Warn "Node.js 18+ required. Installing Node.js 20 LTS..."
            Invoke-WebRequest -Uri "https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi" -OutFile "node-setup.msi"
            Start-Process msiexec.exe -ArgumentList "/i node-setup.msi /quiet /norestart" -Wait
            Remove-Item "node-setup.msi" -Force
            RefreshEnv
        }
        Write-Success "Node.js: OK ($nodeVersion)"
    }
    catch {
        Write-ErrorLog "Node.js not found or installation failed: $_"
        exit 1
    }
    
    # Check PNPM
    try {
        $pnpmVersion = pnpm --version 2>$null
        if (-not $pnpmVersion) {
            Write-Info "Installing PNPM..."
            Invoke-Expression (Invoke-WebRequest https://get.pnpm.io/install.ps1).Content
            RefreshEnv
        }
        Write-Success "PNPM: OK ($pnpmVersion)"
    }
    catch {
        Write-ErrorLog "PNPM installation failed: $_"
        exit 1
    }
    
    # Check Git
    try {
        $gitVersion = git --version 2>$null
        if (-not $gitVersion) {
            Write-Warn "Git not found. Installing Git..."
            Invoke-WebRequest -Uri "https://github.com/git-for-windows/git/releases/download/v2.43.0.windows.1/Git-2.43.0-64-bit.exe" -OutFile "git-setup.exe"
            Start-Process "git-setup.exe" -ArgumentList "/VERYSILENT" -Wait
            Remove-Item "git-setup.exe" -Force
            RefreshEnv
        }
        Write-Success "Git: OK ($gitVersion)"
    }
    catch {
        Write-ErrorLog "Git not found or installation failed: $_"
        exit 1
    }-
    
    # Check Supabase CLI
    try {
        $supabaseVersion = supabase --version 2>$null
        if (-not $supabaseVersion) {
            Write-Info "Installing Supabase CLI..."
            Invoke-WebRequest -Uri "https://github.com/supabase/cli/releases/latest/download/supabase_1.128.0_windows_amd64.zip" -OutFile "supabase.zip"
            Expand-Archive "supabase.zip" -DestinationPath "$env:LOCALAPPDATA\supabase"
            $env:PATH += ";$env:LOCALAPPDATA\supabase"
            [Environment]::SetEnvironmentVariable("PATH", $env:PATH, [EnvironmentVariableTarget]::User)
            Remove-Item "supabase.zip" -Force
            RefreshEnv
        }
        Write-Success "Supabase CLI: OK ($supabaseVersion)"
    }
    catch {
        Write-ErrorLog "Supabase CLI installation failed: $_"
        exit 1
    }
    
    Write-Success "✅ All prerequisites verified!"
}

# =================================================================
# Function: Setup Project Structure
# =================================================================
function Setup-ProjectStructure {
    Write-Info "📁 Setting up project structure..."
    
    # Verify we're in the project root
    if (-not (Test-Path "package.json") -or -not (Test-Path "app") -or -not (Test-Path "components")) {
        Write-ErrorLog "Not in Skillza project root. Expected package.json, app/, and components/ directories."
        exit 1
    }
    
    # Create necessary directories
    $dirs = @(
        "supabase/functions/send-telegram-notification",
        "supabase/migrations",
        "scripts/deployment",
        "dist-static"
    )
    
    foreach ($dir in $dirs) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Info "Created directory: $dir"
        }
    }
    
    # Verify essential files exist
    $essentialFiles = @(
        "app/layout.tsx",
        "components/application-form.tsx", 
        "lib/supabase.ts",
        "scripts/build-static-hostinger.js",
        "package.json"
    )
    
    foreach ($file in $essentialFiles) {
        if (-not (Test-Path $file)) {
            Write-Warn "Essential file missing: $file"
        }
    }
    
    Write-Success "✅ Project structure verified/created"
}

# =================================================================
# Function: Supabase CLI Setup & Login
# =================================================================
function Setup-SupabaseCLI {
    param(
        [string]$AccessToken,
        [string]$ProjectRef
    )
    
    Write-Info "⚙️ Setting up Supabase CLI..."
    
    # Login to Supabase
    if ($AccessToken) {
        supabase login --token $AccessToken
        Write-Success "Logged in with provided token"
    } else {
        Write-Info "Please login to Supabase CLI..."
        supabase login
        if ($LASTEXITCODE -ne 0) {
            Write-ErrorLog "Supabase login failed. Please run 'supabase login' manually."
            exit 1
        }
        Write-Success "✅ Supabase CLI login successful"
    }
    
    # Link to project
    if ($ProjectRef) {
        supabase link --project-ref $ProjectRef
        if ($LASTEXITCODE -ne 0) {
            Write-ErrorLog "Failed to link to project $ProjectRef"
            exit 1
        }
        Write-Success "✅ Linked to project: $ProjectRef"
    } else {
        Write-Info "Please link to your Supabase project..."
        supabase link
        if ($LASTEXITCODE -ne 0) {
            Write-ErrorLog "Project linking failed. Please run 'supabase link' manually."
            exit 1
        }
        Write-Success "✅ Project linked successfully"
    }
    
    # Initialize Supabase local setup if needed
    if (-not (Test-Path "supabase/config.toml")) {
        Write-Info "Initializing Supabase local configuration..."
        supabase init
        Write-Success "✅ Supabase local configuration initialized"
    }
    
    # Verify connection
    Write-Info "Testing Supabase connection..."
    supabase status
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorLog "Supabase connection test failed"
        exit 1
    }
    Write-Success "✅ Supabase CLI setup complete"
}

# =================================================================
# Function: Create Enhanced Edge Function
# =================================================================
function New-TelegramEdgeFunction {
    Write-Info "🌐 Creating enhanced Telegram Edge Function (Sep 2025)..."
    
    # Create function directory
    $functionDir = "supabase/functions/send-telegram-notification"
    if (-not (Test-Path $functionDir)) {
        New-Item -ItemType Directory -Path $functionDir -Force | Out-Null
    }
    
    # Create index.ts with enhanced Sep 2025 Telegram integration
    $edgeFunctionCode = @'
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve({ port: 54321 }, async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { record } = await req.json()
    
    if (!record || !record.name || !record.email || !record.cv_url) {
      return new Response(
        JSON.stringify({ error: 'Missing required application data' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const botToken = Deno.env.get('TELEGRAM_BOT_TOKEN')
    const chatIds = (Deno.env.get('TELEGRAM_CHAT_IDS') || '')
      .split(',')
      .map(id => id.trim())
      .filter(Boolean)
    
    if (!botToken || chatIds.length === 0) {
      console.error('Missing Telegram configuration')
      return new Response(
        JSON.stringify({ error: 'Telegram configuration missing' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const telegramApiUrl = `https://api.telegram.org/bot${botToken}/`
    const language = record.language || 'en'
    const isArabic = language === 'ar'
    const timestamp = new Date().toLocaleString(isArabic ? 'ar-SA' : 'en-US')

    const messageText = isArabic 
      ? `<b>🚨 طلب توظيف جديد - سكيلزا</b>

👤 <b>الاسم:</b> <code>${record.name}</code>
📧 <b>الإيميل:</b> <code>${record.email}</code>
📱 <b>الهاتف:</b> <code>${record.phone}</code>
💼 <b>الوظيفة:</b> ${record.position || 'غير محدد'}
📊 <b>الخبرة:</b> ${record.experience || 'غير محدد'}

📄 <b>السيرة الذاتية:</b>
<a href="${record.cv_url}">📥 تحميل PDF</a>

🆔 <b>معرف الطلب:</b> <code>${record.id || 'N/A'}</code>
⏰ <b>وقت التقديم:</b> ${timestamp}
🌐 <b>اللغة:</b> العربية

<i>تم الإرسال عبر الموقع الإلكتروني - سكيلزا</i>`
      : `<b>🚨 New Job Application - Skillza</b>

👤 <b>Name:</b> <code>${record.name}</code>
📧 <b>Email:</b> <code>${record.email}</code>
📱 <b>Phone:</b> <code>${record.phone}</code>
💼 <b>Position:</b> ${record.position || 'Not specified'}
📊 <b>Experience:</b> ${record.experience || 'Not specified'}

📄 <b>CV:</b>
<a href="${record.cv_url}">📥 Download PDF</a>

🆔 <b>Application ID:</b> <code>${record.id || 'N/A'}</code>
⏰ <b>Submission Time:</b> ${timestamp}
🌐 <b>Language:</b> English

<i>Submitted via web form - Skillza</i>`

    const inlineKeyboard = {
      inline_keyboard: [
        [
          { text: "📥 Download CV", url: record.cv_url },
          { text: "👤 View Profile", callback_data: `view_${record.id || 'unknown'}` }
        ],
        [
          { text: "✅ Shortlist", callback_data: `shortlist_${record.id || 'unknown'}` },
          { text: "📋 Review", callback_data: `review_${record.id || 'unknown'}` },
          { text: "❌ Reject", callback_data: `reject_${record.id || 'unknown'}` }
        ],
        [
          { text: "📧 Email Applicant", url: `mailto:${record.email}?subject=Job Application - ${record.position}` },
          { text: "📱 Call Applicant", url: `tel:${record.phone.replace(/\s+/g, '')}` }
        ]
      ]
    }

    const sendToAllChats = async () => {
      const results = await Promise.allSettled(
        chatIds.map(async (chatId) => {
          let lastError: any = null
          for (let attempt = 1; attempt <= 3; attempt++) {
            try {
              const response = await fetch(`${telegramApiUrl}sendMessage`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'User-Agent': 'Skillza-Bot/2.0 (Edge Function)',
                },
                body: JSON.stringify({
                  chat_id: chatId.trim(),
                  text: messageText,
                  parse_mode: 'HTML',
                  disable_web_page_preview: false,
                  reply_markup: inlineKeyboard,
                  message_thread_id: 1,
                  protect_content: true,
                  disable_notification: false,
                })
              })

              if (!response.ok) {
                const errorData = await response.json()
                lastError = new Error(errorData.description || `HTTP ${response.status}`)
                
                if (errorData.parameters?.retry_after) {
                  const delay = errorData.parameters.retry_after * 1000
                  await new Promise(resolve => setTimeout(resolve, delay))
                } else {
                  const backoffDelay = Math.min(1000 * Math.pow(2, attempt), 30000)
                  await new Promise(resolve => setTimeout(resolve, backoffDelay))
                }
                
                continue
              }

              const result = await response.json()
              console.log(`✅ Message sent to ${chatId}: ID ${result.result.message_id}`)
              
              await supabaseClient
                .from('telegram_deliveries')
                .upsert({
                  application_id: record.id,
                  chat_id: chatId.trim(),
                  message_id: result.result.message_id,
                  status: 'delivered',
                  sent_at: new Date().toISOString(),
                  language: language
                }, { onConflict: 'application_id,chat_id' })

              return { success: true, chatId, messageId: result.result.message_id }
              
            } catch (error) {
              lastError = error
              if (attempt === 3) {
                await supabaseClient
                  .from('telegram_deliveries')
                  .upsert({
                    application_id: record.id,
                    chat_id: chatId.trim(),
                    status: 'failed',
                    error_message: error.message,
                    retry_attempts: 3,
                    sent_at: new Date().toISOString(),
                  }, { onConflict: 'application_id,chat_id' })
                
                return { success: false, chatId, error: error.message }
              }
            }
          }
        })
      )

      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length
      const total = chatIds.length
      const failed = results.filter(r => r.status === 'fulfilled' && !r.value.success).length

      console.log(`📊 Telegram Summary: ${successful}/${total} delivered (${failed} failed)`)

      return { success: successful > 0, successful, total, failed, delivery_rate: Math.round((successful / total) * 100) }
    }

    const deliveryResult = await sendToAllChats()

    await supabaseClient
      .from('job_applications')
      .update({
        telegram_status: deliveryResult.success ? 'delivered' : 'failed',
        telegram_delivery_rate: deliveryResult.delivery_rate,
        telegram_sent_count: deliveryResult.successful,
        telegram_total_count: deliveryResult.total,
        telegram_updated_at: new Date().toISOString()
      })
      .eq('id', record.id)

    return new Response(
      JSON.stringify({
        success: deliveryResult.success,
        message: `Delivered to ${deliveryResult.successful}/${deliveryResult.total} chats`,
        details: deliveryResult,
        application_id: record.id
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Edge Function Error:', error)
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )
    
    await supabaseClient
      .from('telegram_errors')
      .insert({
        error_message: error.message,
        error_stack: error.stack,
        timestamp: new Date().toISOString(),
        request_method: req.method,
        user_agent: req.headers.get('user-agent') || 'unknown'
      })

    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error', timestamp: new Date().toISOString() }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
'@
    
    # Write Edge Function
    $indexTsPath = Join-Path $EdgeFunctionDir "index.ts"
    $edgeFunctionCode | Out-File -FilePath $indexTsPath -Encoding UTF8
    Write-Info "Created Edge Function: $indexTsPath"
    
    # Create supabase/functions.toml if not exists
    $functionsTomlPath = Join-Path $SupabaseDir "functions.toml"
    if (-not (Test-Path $functionsTomlPath)) {
        @"
[functions]
SUPABASE_URL = ""
SUPABASE_SERVICE_ROLE_KEY = ""
SUPABASE_ANON_KEY = ""

[functions.send-telegram-notification]
"@
        | Out-File -FilePath $functionsTomlPath -Encoding UTF8
    }
    
    Write-Success "✅ Enhanced Telegram Edge Function created"
}

# =================================================================
# Function: Create Database Migrations
# =================================================================
function New-DatabaseMigrations {
    Write-Info "📊 Creating database migrations for Telegram integration..."
    
    $migrationDir = "supabase/migrations"
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $migrationFile = Join-Path $migrationDir "${timestamp}_enhanced_telegram_integration.sql"
    
    $migrationSql = @'
-- Enhanced Telegram Notification System - Sep 2025
-- Advanced trigger with rich messages, inline keyboards, and delivery tracking

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_net";  -- For HTTP requests

-- 1. Create delivery tracking table
CREATE TABLE IF NOT EXISTS telegram_deliveries (
  id BIGSERIAL PRIMARY KEY,
  application_id UUID REFERENCES job_applications(id) ON DELETE CASCADE,
  chat_id TEXT NOT NULL,
  message_id BIGINT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'delivered', 'failed', 'retrying')),
  error_message TEXT,
  retry_attempts INTEGER DEFAULT 0,
  delivery_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  language TEXT CHECK (language IN ('en', 'ar')),
  bot_version TEXT DEFAULT '2.0',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_app_id ON telegram_deliveries(application_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_status ON telegram_deliveries(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_chat_id ON telegram_deliveries(chat_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_timestamp ON telegram_deliveries(delivery_timestamp);

-- 2. Create error logging table
CREATE TABLE IF NOT EXISTS telegram_errors (
  id BIGSERIAL PRIMARY KEY,
  application_id UUID REFERENCES job_applications(id) ON DELETE SET NULL,
  error_message TEXT NOT NULL,
  error_stack TEXT,
  request_method TEXT,
  user_agent TEXT,
  ip_address INET,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved BOOLEAN DEFAULT false
);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_errors_timestamp ON telegram_errors(timestamp);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_errors_app_id ON telegram_errors(application_id);

-- 3. Add delivery status columns to job_applications
ALTER TABLE job_applications 
ADD COLUMN IF NOT EXISTS telegram_status TEXT DEFAULT 'pending' 
CHECK (telegram_status IN ('pending', 'delivered', 'partial_delivery', 'failed')),
ADD COLUMN IF NOT EXISTS telegram_sent_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS telegram_total_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS telegram_delivery_rate INTEGER CHECK (telegram_delivery_rate BETWEEN 0 AND 100),
ADD COLUMN IF NOT EXISTS telegram_updated_at TIMESTAMP WITH TIME ZONE;

-- 4. Create app_settings table for configuration
CREATE TABLE IF NOT EXISTS app_settings (
  id SERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert Telegram configuration (update with your actual values)
INSERT INTO app_settings (key, value, description) 
VALUES 
  ('telegram_bot_token', 'YOUR_BOT_TOKEN_HERE', 'Telegram Bot Token for notifications'),
  ('telegram_chat_ids', '-1001234567890,-1000987654321,123456789', 'Comma-separated Telegram chat IDs'),
  ('supabase_url', 'https://aqvztdxidpfirjvovhyi.supabase.co', 'Supabase project URL'),
  ('service_role_key', 'YOUR_SERVICE_ROLE_KEY', 'Supabase service role key')
ON CONFLICT (key) DO UPDATE SET 
  value = EXCLUDED.value,
  updated_at = NOW();

-- 5. Create enhanced trigger function
CREATE OR REPLACE FUNCTION enhanced_telegram_notification()
RETURNS TRIGGER AS 
$$
DECLARE
  bot_token TEXT;
  chat_ids TEXT[];
  message_text TEXT;
  inline_keyboard JSONB;
  http_response JSONB;
  success_count INTEGER := 0;
  total_chats INTEGER;
BEGIN
  -- Get configuration
  SELECT value INTO bot_token FROM app_settings WHERE key = 'telegram_bot_token';
  SELECT string_to_array(value, ',') INTO chat_ids FROM app_settings WHERE key = 'telegram_chat_ids';
  
  IF bot_token IS NULL OR chat_ids IS NULL OR array_length(chat_ids, 1) IS NULL THEN
    RAISE NOTICE 'Telegram configuration missing - skipping notification';
    RETURN NEW;
  END IF;

  total_chats := array_length(chat_ids, 1);
  
  -- Prepare message based on language
  IF COALESCE(NEW.language, 'en') = 'ar' THEN
    message_text := 
      '<b>🚨 طلب توظيف جديد - سكيلزا</b>' || E'\n\n' ||
      '👤 <b>الاسم:</b> <code>' || NEW.name || '</code>' || E'\n' ||
      '📧 <b>البريد:</b> <code>' || NEW.email || '</code>' || E'\n' ||
      '📱 <b>الهاتف:</b> <code>' || NEW.phone || '</code>' || E'\n' ||
      '💼 <b>الوظيفة:</b> ' || COALESCE(NEW.position, 'غير محدد') || E'\n' ||
      '📊 <b>الخبرة:</b> ' || COALESCE(NEW.experience, 'غير محدد') || E'\n\n' ||
      '📄 <b>السيرة الذاتية:</b>' || E'\n' ||
      '<a href="' || NEW.cv_url || '">📥 تحميل PDF</a>' || E'\n\n' ||
      '🆔 <b>رقم الطلب:</b> <code>' || NEW.id || '</code>' || E'\n' ||
      '⏰ <b>التاريخ:</b> ' || to_char(NOW(), 'DD/MM/YYYY HH24:MI') || E'\n' ||
      '<i>تم الإرسال عبر الموقع الإلكتروني</i>';
  ELSE
    message_text := 
      '<b>🚨 New Job Application - Skillza</b>' || E'\n\n' ||
      '👤 <b>Name:</b> <code>' || NEW.name || '</code>' || E'\n' ||
      '📧 <b>Email:</b> <code>' || NEW.email || '</code>' || E'\n' ||
      '📱 <b>Phone:</b> <code>' || NEW.phone || '</code>' || E'\n' ||
      '💼 <b>Position:</b> ' || COALESCE(NEW.position, 'Not specified') || E'\n' ||
      '📊 <b>Experience:</b> ' || COALESCE(NEW.experience, 'Not specified') || E'\n\n' ||
      '📄 <b>CV:</b>' || E'\n' ||
      '<a href="' || NEW.cv_url || '">📥 Download PDF</a>' || E'\n\n' ||
      '🆔 <b>Application ID:</b> <code>' || NEW.id || '</code>' || E'\n' ||
      '⏰ <b>Date:</b> ' || to_char(NOW(), 'MM/DD/YYYY HH24:MI') || E'\n' ||
      '<i>Submitted via web form</i>';
  END IF;

  -- Inline keyboard for actions
  inline_keyboard := json_build_object(
    'inline_keyboard', json_build_array(
      json_build_array(
        json_build_object('text', '📥 Download CV', 'url', NEW.cv_url),
        json_build_object('text', '👤 Profile', 'callback_data', 'profile_' || NEW.id)
      ),
      json_build_array(
        json_build_object('text', '✅ Shortlist', 'callback_data', 'shortlist_' || NEW.id),
        json_build_object('text', '📋 Review', 'callback_data', 'review_' || NEW.id),
        json_build_object('text', '❌ Reject', 'callback_data', 'reject_' || NEW.id)
      )
    )
  );

  -- Insert pending delivery records
  INSERT INTO telegram_deliveries (application_id, chat_id, status, language)
  SELECT NEW.id, unnest(chat_ids), 'pending', COALESCE(NEW.language, 'en');

  -- Call Edge Function for parallel processing
  PERFORM net.http_post(
    url := (SELECT value FROM app_settings WHERE key = 'supabase_url') || '/functions/v1/send-telegram-notification',
    headers := jsonb_build_object(
      'Authorization', 'Bearer ' || (SELECT value FROM app_settings WHERE key = 'service_role_key'),
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object('record', row_to_json(NEW)::jsonb)
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
DROP TRIGGER IF EXISTS job_application_telegram_trigger ON job_applications;
CREATE TRIGGER job_application_telegram_trigger
  AFTER INSERT ON job_applications
  FOR EACH ROW
  EXECUTE FUNCTION enhanced_telegram_notification();

-- Create retry function
CREATE OR REPLACE FUNCTION retry_failed_telegram_deliveries()
RETURNS void AS 
$$
DECLARE
  failed_record RECORD;
BEGIN
  FOR failed_record IN 
    SELECT * FROM telegram_deliveries 
    WHERE status = 'failed' AND retry_attempts < 3 
    AND delivery_timestamp > NOW() - INTERVAL '1 hour'
  LOOP
    -- Update to retrying status
    UPDATE telegram_deliveries 
    SET status = 'retrying', retry_attempts = retry_attempts + 1
    WHERE id = failed_record.id;
    
    -- This will trigger the main function again
    RAISE NOTICE 'Retrying delivery for application %', failed_record.application_id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on new tables
ALTER TABLE telegram_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE telegram_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Service role can manage deliveries" ON telegram_deliveries
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage errors" ON telegram_errors
  FOR ALL USING (auth.role() = 'service_role');

-- Create analytics view
CREATE OR REPLACE VIEW telegram_analytics AS
SELECT 
  date_trunc('hour', delivery_timestamp) as hour,
  language,
  COUNT(*) as total_messages,
  COUNT(*) FILTER (WHERE status = 'delivered') as delivered,
  COUNT(*) FILTER (WHERE status = 'failed') as failed,
  ROUND(AVG(CASE WHEN status = 'delivered' THEN 100.0 ELSE 0 END)) as delivery_rate_pct,
  COUNT(DISTINCT application_id) as unique_applications
FROM telegram_deliveries
GROUP BY date_trunc('hour', delivery_timestamp), language
ORDER BY hour DESC;

Write-Success "✅ Database migration created: $migrationFile"
}

# =================================================================
# Function: Fix MIME Types - Enhanced .htaccess
# =================================================================
function Update-HtaccessMimeTypes {
    Write-Info "🔧 Fixing MIME types for Hostinger static hosting..."
    
    $htaccessContent = @'
# Hostinger Premium Static Hosting - Next.js Static Export (Sep 2025)
# Optimized for LiteSpeed Web Server with MIME Type Fixes

# Enable Rewrite Engine
RewriteEngine On

# ==================== CRITICAL: MIME TYPE FIXES ====================
# Fix CSS files - Force text/css (Solves the main error)
<FilesMatch "\.css$">
    ForceType text/css
    Header set Content-Type "text/css; charset=utf-8"
</FilesMatch>

# Fix JavaScript files - Force application/javascript
<FilesMatch "\.js$">
    ForceType application/javascript
    Header set Content-Type "application/javascript; charset=utf-8"
</FilesMatch>

# Fix JSON files - Force application/json
<FilesMatch "\.json$">
    ForceType application/json
    Header set Content-Type "application/json; charset=utf-8"
</FilesMatch>

# Fix HTML files - Force text/html
<FilesMatch "\.html?$">
    ForceType text/html
    Header set Content-Type "text/html; charset=utf-8"
</FilesMatch>

# Fix image files
<FilesMatch "\.(png|jpg|jpeg|gif|webp|svg|ico|bmp)$">
    Header set Content-Type "image/%{REQUEST_EXTENSION}e"
</FilesMatch>

# Fix font files
<FilesMatch "\.(woff|woff2|ttf|eot|otf)$">
    Header set Content-Type "font/%{REQUEST_EXTENSION}e"
</FilesMatch>

# ==================== SPA ROUTING (Next.js Static Export) ====================
# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^ /index.html [L]

# ==================== COMPRESSION (Brotli + GZIP) ====================
<IfModule mod_brotli.c>
    AddOutputFilterByType BROTLI_COMPRESS text/html text/css text/javascript application/json
</IfModule>

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css text/javascript application/json
</IfModule>

# ==================== CACHING ====================
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|ico|svg|woff|woff2)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header set Cache-Control "public, immutable"
</FilesMatch>

<FilesMatch "\.html$">
    ExpiresActive On
    ExpiresDefault "access plus 10 minutes"
</FilesMatch>

# ==================== SECURITY HEADERS ====================
Header set X-Content-Type-Options nosniff
Header set X-Frame-Options DENY
Header set X-XSS-Protection "1; mode=block"
Header set Referrer-Policy "strict-origin-when-cross-origin"

# ==================== PRELOAD & PREFETCH ====================
<FilesMatch "\.html$">
    Header add Link "</css/globals.css>; rel=preload; as=style"
    Header add Link "<https://fonts.googleapis.com>; rel=dns-prefetch"
    Header add Link "<https://aqvztdxidpfirjvovhyi.supabase.co>; rel=dns-prefetch"
</FilesMatch>

# ==================== CORS FOR SUPABASE ====================
Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header set Access-Control-Allow-Headers "Content-Type, Authorization"
'@
    
    $htaccessContent | Out-File -FilePath $HtaccessPath -Encoding ASCII
    Write-Success "✅ Enhanced .htaccess created with MIME type fixes: $HtaccessPath"
}

# =================================================================
# Function: Build Static for Hostinger
# =================================================================
function Build-StaticHostinger {
    Write-Info "🏗️ Building static export for Hostinger..."
    
    # Clean previous builds
    if (Test-Path $DistStaticDir) {
        Remove-Item $DistStaticDir -Recurse -Force
    }
    if (Test-Path ".next") {
        Remove-Item ".next" -Recurse -Force
    }
    if (Test-Path "out") {
        Remove-Item "out" -Recurse -Force
    }
    
    # Install dependencies
    Write-Info "Installing dependencies with PNPM..."
    pnpm install --frozen-lockfile
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorLog "PNPM install failed"
        exit 1
    }
    
    # Type check
    Write-Info "Running TypeScript check..."
    pnpm run type-check
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorLog "TypeScript check failed"
        exit 1
    }
    
    # Lint
    Write-Info "Running lint..."
    pnpm run lint
    if ($LASTEXITCODE -ne 0) {
        Write-Warn "Lint failed but continuing..."
    }
    
    # Pre-build script
    if (Test-Path $BuildScript) {
        Write-Info "Running pre-build script..."
        node $BuildScript
        if ($LASTEXITCODE -ne 0) {
            Write-Warn "Pre-build script failed but continuing..."
        }
    }
    
    # Build static
    Write-Info "Building Next.js static export..."
    pnpm run build:static
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorLog "Static build failed"
        exit 1
    }
    
    # Verify build output
    if (-not (Test-Path $DistStaticDir)) {
        Write-ErrorLog "Static build output not found in $DistStaticDir"
        exit 1
    }
    
    $buildFiles = Get-ChildItem $DistStaticDir -Recurse | Measure-Object -Property Length -Sum
    Write-Success "✅ Static build complete! Total size: $([math]::Round($buildFiles.Sum / 1MB, 2)) MB"
    Write-Success "Build output: $DistStaticDir"
    
    # Copy .htaccess to build output
    Copy-Item $HtaccessPath $DistStaticDir -Force
    Write-Success "✅ .htaccess copied to build output"
}

# =================================================================
# Function: Deploy Edge Function
# =================================================================
function Deploy-EdgeFunction {
    param(
        [string]$TelegramBotToken,
        [string[]]$TelegramChatIds
    )
    
    Write-Info "🚀 Deploying enhanced Telegram Edge Function..."
    
    # Set environment variables for function
    $envVars = @{
        SUPABASE_URL = $env:NEXT_PUBLIC_SUPABASE_URL
        SUPABASE_SERVICE_ROLE_KEY = $env:SUPABASE_SERVICE_ROLE_KEY
        TELEGRAM_BOT_TOKEN = $TelegramBotToken
        TELEGRAM_CHAT_IDS = ($TelegramChatIds -join ",")
    }
    
    # Create/update functions config
    $functionsConfig = @{
        functions = @{
            SUPABASE_URL = $envVars.SUPABASE_URL
            SUPABASE_SERVICE_ROLE_KEY = $envVars.SUPABASE_SERVICE_ROLE_KEY
            TELEGRAM_BOT_TOKEN = $envVars.TELEGRAM_BOT_TOKEN
            TELEGRAM_CHAT_IDS = $envVars.TELEGRAM_CHAT_IDS
        }
        "functions.send-telegram-notification" = @()
    }
    
    # Deploy function
    Write-Info "Deploying send-telegram-notification function..."
    supabase functions deploy send-telegram-notification --no-verify-jwt
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorLog "Edge function deployment failed"
        exit 1
    }
    
    Write-Success "✅ Edge Function deployed successfully"
    
    # Test function
    Write-Info "🧪 Testing Edge Function..."
    $testPayload = @{
        record = @{
            id = "test-$(Get-Date -Format 'yyyyMMddHHmmss')"
            name = "Test User"
            email = "<EMAIL>"
            phone = "+966501234567"
            position = "Test Position"
            experience = "3-5 years"
            cv_url = "https://example.com/test-cv.pdf"
            language = "en"
        }
    } | ConvertTo-Json -Depth 3
    
    $headers = @{
        "Authorization" = "Bearer $($env:SUPABASE_SERVICE_ROLE_KEY)"
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$($env:NEXT_PUBLIC_SUPABASE_URL)/functions/v1/send-telegram-notification" -Method POST -Headers $headers -Body $testPayload
    
    if ($response.success) {
        Write-Success "✅ Edge Function test successful: $($response.message)"
    } else {
        Write-Warn "Edge Function test completed but may need configuration: $($response.error)"
    }
}

# =================================================================
# Function: Apply Database Migrations
# =================================================================
function Invoke-DatabaseMigrations {
    Write-Info "📊 Applying database migrations..."
    
    # Generate migration timestamp
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $migrationFile = Join-Path $MigrationsDir "${timestamp}_enhanced_telegram_integration.sql"
    
    # Create migration content
    $migrationContent = @'
-- Enhanced Telegram Notification System - Sep 2025
-- Advanced trigger with rich messages, inline keyboards, and delivery tracking

-- 1. Create delivery tracking table
CREATE TABLE IF NOT EXISTS public.telegram_deliveries (
  id BIGSERIAL PRIMARY KEY,
  application_id UUID REFERENCES public.job_applications(id) ON DELETE CASCADE,
  chat_id TEXT NOT NULL,
  message_id BIGINT,
  status TEXT NOT NULL DEFAULT ''pending'' CHECK (status IN (''pending'', ''delivered'', ''failed'', ''retrying'')),
  error_message TEXT,
  retry_attempts INTEGER DEFAULT 0,
  delivery_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  language TEXT CHECK (language IN (''en'', ''ar'')),
  bot_version TEXT DEFAULT ''2.0'',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_app_id ON public.telegram_deliveries USING btree (application_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_status ON public.telegram_deliveries USING btree (status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_chat_id ON public.telegram_deliveries USING btree (chat_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_timestamp ON public.telegram_deliveries USING btree (delivery_timestamp);

-- 2. Create error logging table
CREATE TABLE IF NOT EXISTS public.telegram_errors (
  id BIGSERIAL PRIMARY KEY,
  application_id UUID REFERENCES public.job_applications(id) ON DELETE SET NULL,
  error_message TEXT NOT NULL,
  error_stack TEXT,
  request_method TEXT,
  user_agent TEXT,
  ip_address INET,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved BOOLEAN DEFAULT false
);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_errors_timestamp ON public.telegram_errors USING btree (timestamp);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_errors_app_id ON public.telegram_errors USING btree (application_id);

-- 3. Add delivery columns to job_applications
ALTER TABLE public.job_applications 
ADD COLUMN IF NOT EXISTS telegram_status TEXT DEFAULT ''pending'' 
CHECK (telegram_status IN (''pending'', ''delivered'', ''partial_delivery'', ''failed'')),
ADD COLUMN IF NOT EXISTS telegram_sent_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS telegram_total_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS telegram_delivery_rate INTEGER CHECK (telegram_delivery_rate BETWEEN 0 AND 100),
ADD COLUMN IF NOT EXISTS telegram_updated_at TIMESTAMP WITH TIME ZONE;

-- 4. Create configuration table
CREATE TABLE IF NOT EXISTS public.app_settings (
  id SERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default Telegram configuration (UPDATE with your values)
INSERT INTO public.app_settings (key, value, description) 
VALUES 
  (''telegram_bot_token'', ''YOUR_BOT_TOKEN_HERE'', ''Telegram Bot Token''),
  (''telegram_chat_ids'', ''-1001234567890,-1000987654321,123456789'', ''Comma-separated chat IDs''),
  (''supabase_url'', ''https://aqvztdxidpfirjvovhyi.supabase.co'', ''Supabase URL''),
  (''service_role_key'', ''YOUR_SERVICE_ROLE_KEY'', ''Service role key'')
ON CONFLICT (key) DO UPDATE SET 
  value = EXCLUDED.value,
  updated_at = NOW();

-- 5. Create enhanced trigger function
CREATE OR REPLACE FUNCTION public.enhanced_telegram_notification()
RETURNS TRIGGER AS 
$$ 
DECLARE
  bot_token TEXT;
  chat_ids TEXT[];
  message_text TEXT;
  inline_keyboard JSONB;
  http_response JSONB;
  success_count INTEGER := 0;
  total_chats INTEGER;
BEGIN
  -- Get configuration
  SELECT value INTO bot_token FROM public.app_settings WHERE key = ''telegram_bot_token'';
  SELECT string_to_array(value, '')'') INTO chat_ids FROM public.app_settings WHERE key = ''telegram_chat_ids'';
  
  IF bot_token IS NULL OR chat_ids IS NULL OR array_length(chat_ids, 1) IS NULL THEN
    RAISE NOTICE ''Telegram configuration missing - skipping notification'';
    RETURN NEW;
  END IF;

  total_chats := array_length(chat_ids, 1);
  
  -- Prepare bilingual message
  IF COALESCE(NEW.language, ''en'') = ''ar'' THEN
    message_text := 
      ''<b>🚨 طلب توظيف جديد - سكيلزا</b>'' || E''\n\n'' ||
      ''👤 <b>الاسم:</b> <code>'' || NEW.name || ''</code>'' || E''\n'' ||
      ''📧 <b>البريد:</b> <code>'' || NEW.email || ''</code>'' || E''\n'' ||
      ''📱 <b>الهاتف:</b> <code>'' || NEW.phone || ''</code>'' || E''\n'' ||
      ''💼 <b>الوظيفة:</b> '' || COALESCE(NEW.position, ''غير محدد'') || E''\n'' ||
      ''📊 <b>الخبرة:</b> '' || COALESCE(NEW.experience, ''غير محدد'') || E''\n\n'' ||
      ''📄 <b>السيرة الذاتية:</b>'' || E''\n'' ||
      ''<a href="'' || NEW.cv_url || ''">📥 تحميل PDF</a>'' || E''\n\n'' ||
      ''🆔 <b>رقم الطلب:</b> <code>'' || NEW.id || ''</code>'' || E''\n'' ||
      ''⏰ <b>التاريخ:</b> '' || to_char(NOW(), ''DD/MM/YYYY HH24:MI'') || E''\n'' ||
      ''<i>تم الإرسال عبر الموقع الإلكتروني</i>'';
  ELSE
    message_text := 
      ''<b>🚨 New Job Application - Skillza</b>'' || E''\n\n'' ||
      ''👤 <b>Name:</b> <code>'' || NEW.name || ''</code>'' || E''\n'' ||
      ''📧 <b>Email:</b> <code>'' || NEW.email || ''</code>'' || E''\n'' ||
      ''📱 <b>Phone:</b> <code>'' || NEW.phone || ''</code>'' || E''\n'' ||
      ''💼 <b>Position:</b> '' || COALESCE(NEW.position, ''Not specified'') || E''\n'' ||
      ''📊 <b>Experience:</b> '' || COALESCE(NEW.experience, ''Not specified'') || E''\n\n'' ||
      ''📄 <b>CV:</b>'' || E''\n'' ||
      ''<a href="'' || NEW.cv_url || ''">📥 Download PDF</a>'' || E''\n\n'' ||
      ''🆔 <b>Application ID:</b> <code>'' || NEW.id || ''</code>'' || E''\n'' ||
      ''⏰ <b>Date:</b> '' || to_char(NOW(), ''MM/DD/YYYY HH24:MI'') || E''\n'' ||
      ''<i>Submitted via web form</i>'';
  END IF;

  -- Inline keyboard
  inline_keyboard := json_build_object(
    ''inline_keyboard'', json_build_array(
      json_build_array(
        json_build_object(''text'', ''📥 Download CV'', ''url'', NEW.cv_url),
        json_build_object(''text'', ''👤 Profile'', ''callback_data'', ''profile_'' || NEW.id)
      ),
      json_build_array(
        json_build_object(''text'', ''✅ Shortlist'', ''callback_data'', ''shortlist_'' || NEW.id),
        json_build_object(''text'', ''📋 Review'', ''callback_data'', ''review_'' || NEW.id),
        json_build_object(''text'', ''❌ Reject'', ''callback_data'', ''reject_'' || NEW.id)
      )
    )
  );

  -- Create pending delivery records
  INSERT INTO public.telegram_deliveries (application_id, chat_id, status, language)
  SELECT NEW.id, unnest(chat_ids), ''pending'', COALESCE(NEW.language, ''en'');
  
  -- Call Edge Function
  PERFORM net.http_post(
    url := (SELECT value FROM public.app_settings WHERE key = ''supabase_url'') || ''/functions/v1/send-telegram-notification'',
    headers := jsonb_build_object(
      ''Authorization'', ''Bearer '' || (SELECT value FROM public.app_settings WHERE key = ''service_role_key''),
      ''Content-Type'', ''application/json''
    ),
    body := jsonb_build_object(''record'', row_to_json(NEW)::jsonb)
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
DROP TRIGGER IF EXISTS job_application_telegram_trigger ON public.job_applications;
CREATE TRIGGER job_application_telegram_trigger
  AFTER INSERT ON public.job_applications
  FOR EACH ROW
  EXECUTE FUNCTION public.enhanced_telegram_notification();

-- Create retry function
CREATE OR REPLACE FUNCTION public.retry_failed_telegram_deliveries()
RETURNS void AS 
$$
DECLARE
  failed_record RECORD;
BEGIN
  FOR failed_record IN 
    SELECT * FROM public.telegram_deliveries 
    WHERE status = ''failed'' AND retry_attempts < 3 
    AND delivery_timestamp > NOW() - INTERVAL ''1 hour''
  LOOP
    UPDATE public.telegram_deliveries 
    SET status = ''retrying'', retry_attempts = retry_attempts + 1
    WHERE id = failed_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS
ALTER TABLE public.telegram_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.telegram_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Service role can manage deliveries" ON public.telegram_deliveries
  FOR ALL USING (auth.role() = ''service_role'');

CREATE POLICY "Service role can manage errors" ON public.telegram_errors
  FOR ALL USING (auth.role() = ''service_role'');

-- Analytics view
CREATE OR REPLACE VIEW public.telegram_analytics AS
SELECT 
  date_trunc(''hour'', delivery_timestamp) as hour,
  language,
  COUNT(*) as total_messages,
  COUNT(*) FILTER (WHERE status = ''delivered'') as delivered,
  COUNT(*) FILTER (WHERE status = ''failed'') as failed,
  ROUND(AVG(CASE WHEN status = ''delivered'' THEN 100.0 ELSE 0 END)) as delivery_rate_pct,
  COUNT(DISTINCT application_id) as unique_applications
FROM public.telegram_deliveries
GROUP BY date_trunc(''hour'', delivery_timestamp), language
ORDER BY hour DESC;
'@
    
    $migrationContent | Out-File -FilePath $migrationFile -Encoding UTF8
    Write-Info "Created migration: $migrationFile"
    
    # Apply migration
    Write-Info "Applying database migration..."
    supabase db push
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorLog "Database migration failed"
        Write-Info "You may need to run this manually in Supabase SQL Editor:"
        Write-Info $migrationFile
        exit 1
    }
    
    Write-Success "✅ Database migration applied successfully"
}

# =================================================================
# Function: Complete Deployment
# =================================================================
function Invoke-CompleteDeployment {
    param(
        [string]$TelegramBotToken,
        [string[]]$TelegramChatIds
    )
    
    Write-Info "🚀 Starting complete deployment..."
    Write-Host "`n" + "="*80 + "`n"
    Write-Host " SKILLZA COMPLETE DEPLOYMENT - SEP 2025" -ForegroundColor Cyan
    Write-Host " Enhanced Telegram Bot + Hostinger Static Hosting" -ForegroundColor Cyan
    Write-Host "="*80 + "`n"
    
    # Phase 1: Prerequisites
    if ($Mode -in @("full", "setup", "deploy")) {
        Write-Info "Phase 1/6: Checking prerequisites..."
        Test-Prerequisites
        Write-Host " " -NoNewline
    }
    
    # Phase 2: Project Structure
    if ($Mode -in @("full", "setup")) {
        Write-Info "Phase 2/6: Setting up project structure..."
        Setup-ProjectStructure
        Write-Host " " -NoNewline
    }
    
    # Phase 3: Supabase CLI
    if ($Mode -in @("full", "setup")) {
        Write-Info "Phase 3/6: Setting up Supabase CLI..."
        Setup-SupabaseCLI -AccessToken $SupabaseAccessToken -ProjectRef $ProjectRef
        Write-Host " " -NoNewline
    }
    
    # Phase 4: Edge Function
    if ($Mode -in @("full", "deploy")) {
        Write-Info "Phase 4/6: Creating Telegram Edge Function..."
        New-TelegramEdgeFunction
        Write-Host " " -NoNewline
    }
    
    # Phase 5: Database
    if ($Mode -in @("full", "deploy")) {
        Write-Info "Phase 5/6: Applying database migrations..."
        New-DatabaseMigrations
        Write-Host " " -NoNewline
    }
    
    # Phase 6: Deploy Edge Function
    if ($Mode -in @("full", "deploy") -and $TelegramBotToken -and $TelegramChatIds) {
        Write-Info "Phase 6/6: Deploying Edge Function..."
        Deploy-EdgeFunction -TelegramBotToken $TelegramBotToken -TelegramChatIds $TelegramChatIds
        Write-Host " " -NoNewline
    }
    
    # Phase 7: Build
    if ($Mode -in @("full", "build")) {
        Write-Info "Phase 7/7: Building for Hostinger..."
        Build-StaticHostinger
        Write-Host " " -NoNewline
    }
    
    # Phase 8: MIME Type Fix
    if ($Mode -in @("full", "build")) {
        Write-Info "Phase 8/8: Fixing MIME types..."
        Update-HtaccessMimeTypes
        Write-Success "✅ MIME type fixes applied"
    }
    
    Write-Success "`n🎉 Complete deployment successful!"
    Write-Host "`n📋 Summary:" -ForegroundColor Green
    Write-Host "  • Supabase CLI: Configured ✓" -ForegroundColor Green
    Write-Host "  • Edge Function: Deployed ✓" -ForegroundColor Green
    Write-Host "  • Database: Migrated ✓" -ForegroundColor Green
    Write-Host "  • Static Build: Ready for Hostinger ✓" -ForegroundColor Green
    Write-Host "  • MIME Types: Fixed ✓" -ForegroundColor Green
    
    if ($Mode -in @("full", "build")) {
        Write-Host "`n📁 Build output ready in: $DistStaticDir" -ForegroundColor Yellow
        Write-Host "🚀 Upload contents of $DistStaticDir to Hostinger public_html" -ForegroundColor Yellow
    }
    
    if ($Mode -in @("full", "deploy")) {
        Write-Host "`n🧪 Test the integration:" -ForegroundColor Yellow
        Write-Host "  1. Submit a test application form" -ForegroundColor Yellow
        Write-Host "  2. Check Telegram chats for notifications" -ForegroundColor Yellow
        Write-Host "  3. Verify CV download links work" -ForegroundColor Yellow
        Write-Host "  4. Check Supabase > telegram_deliveries table" -ForegroundColor Yellow
    }
}

# =================================================================
# Function: Test Integration
# =================================================================
function Test-Integration {
    Write-Info "🧪 Testing complete integration..."
    
    # Test 1: Edge Function
    Write-Info "Testing Edge Function..."
    $testPayload = @{
        record = @{
            id = "test-$(Get-Date -Format 'yyyyMMddHHmmss')"
            name = "Test User $(Get-Random)"
            email = "test+$(Get-Random)@skillza.com"
            phone = "+966501234567"
            position = "Test Developer"
            experience = "3-5 years"
            cv_url = "https://example.com/test-cv.pdf"
            language = "en"
        }
    } | ConvertTo-Json -Depth 3 -Compress
    
    $headers = @{
        "Authorization" = "Bearer $env:SUPABASE_SERVICE_ROLE_KEY"
        "Content-Type" = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "$env:NEXT_PUBLIC_SUPABASE_URL/functions/v1/send-telegram-notification" -Method POST -Headers $headers -Body $testPayload
        if ($response.success) {
            Write-Success "✅ Edge Function test passed: $($response.message)"
        } else {
            Write-Warn "Edge Function responded but may need configuration: $($response.error)"
        }
    }
    catch {
        Write-Warn "Edge Function test failed (expected if not configured): $_"
    }
    
    # Test 2: Build
    Write-Info "Testing static build..."
    pnpm run build:static
    if ($LASTEXITCODE -eq 0 -and (Test-Path $DistStaticDir)) {
        Write-Success "✅ Static build test passed"
    } else {
        Write-ErrorLog "Static build test failed"
        exit 1
    }
    
    # Test 3: Local server (optional)
    if ((Read-Host "Test local server? (y/n)") -eq "y") {
        Write-Info "Starting local server on port 3000..."
        Start-Process "pnpm" "run start" -WorkingDirectory $ProjectRoot
        Start-Sleep 3
        Start-Process "http://localhost:3000"
    }
    
    Write-Success "✅ All integration tests passed!"
}

# =================================================================
# Function: Refresh Environment Variables
# =================================================================
function RefreshEnv {
    $env:PATH = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    Write-Host "Environment refreshed" -ForegroundColor Gray
}

# =================================================================
# Main Execution
# =================================================================
Write-Host "Skillza Deployment Script" -ForegroundColor Cyan
Write-Host "Current Directory: $ProjectRoot" -ForegroundColor Gray
Write-Host "Mode: $Mode" -ForegroundColor Gray
Write-Host "Project Ref: $ProjectRef" -ForegroundColor Gray
Write-Host ""

try {
    # Validate parameters
    if ($Mode -eq "deploy" -and -not $TelegramBotToken) {
        $TelegramBotToken = Read-Host "Enter Telegram Bot Token"
    }
    
    if ($Mode -in @("deploy", "full") -and -not $TelegramChatIds) {
        $chatInput = Read-Host "Enter Telegram Chat IDs (comma-separated)"
        $TelegramChatIds = $chatInput -split "," | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
    }
    
    # Execute based on mode
    switch ($Mode) {
        "check" { Test-Prerequisites }
        "setup" { 
            Test-Prerequisites
            Setup-ProjectStructure
            Setup-SupabaseCLI -AccessToken $SupabaseAccessToken -ProjectRef $ProjectRef
        }
        "build" { 
            Test-Prerequisites
            Build-StaticHostinger
            Update-HtaccessMimeTypes
        }
        "deploy" { 
            Test-Prerequisites
            Setup-ProjectStructure
            Setup-SupabaseCLI -AccessToken $SupabaseAccessToken -ProjectRef $ProjectRef
            New-TelegramEdgeFunction
            New-DatabaseMigrations
            Deploy-EdgeFunction -TelegramBotToken $TelegramBotToken -TelegramChatIds $TelegramChatIds
        }
        "test" { Test-Integration }
        "full" { 
            Test-Prerequisites
            Setup-ProjectStructure
            Setup-SupabaseCLI -AccessToken $SupabaseAccessToken -ProjectRef $ProjectRef
            New-TelegramEdgeFunction
            New-DatabaseMigrations
            Deploy-EdgeFunction -TelegramBotToken $TelegramBotToken -TelegramChatIds $TelegramChatIds
            Build-StaticHostinger
            Update-HtaccessMimeTypes
            Test-Integration
        }
        default { 
            Write-ErrorLog "Invalid mode: $Mode. Use: check, setup, build, deploy, test, full"
            exit 1
        }
    }
    
    Write-Success "`n🎉 Deployment completed successfully!"
    Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
    Write-Host "  1. Upload $DistStaticDir contents to Hostinger public_html" -ForegroundColor Yellow
    Write-Host "  2. Clear Hostinger cache (hPanel → Performance → Purge All)" -ForegroundColor Yellow
    Write-Host "  3. Test form submission at https://skillzajobs.com/apply" -ForegroundColor Yellow
    Write-Host "  4. Check Telegram for notifications" -ForegroundColor Yellow
    Write-Host "  5. Monitor Supabase → telegram_deliveries table" -ForegroundColor Yellow
    
}
catch {
    Write-ErrorLog "Deployment failed: $_"
    Write-ErrorLog "Stack trace: $($_.ScriptStackTrace)"
    exit 1
}

# Clean up any temporary files
Get-ChildItem -Path $ProjectRoot -Filter "*.tmp" -Recurse | Remove-Item -Force
Write-Success "Cleanup complete"