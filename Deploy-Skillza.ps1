<#
.SYNOPSIS
    Deployment script for Skillza-Web: Handles Supabase setup, Edge Functions, migrations, static build, and testing.
    Smarter error handling for Supabase CLI linking with URL parsing and token fallbacks.

.PARAMETER Mode
    Execution mode: full (all), setup (prereqs + Supabase), build (static build), deploy (Edge + migrations), test (verify).

.PARAMETER SupabaseProjectUrl
    Full Supabase project URL (e.g., https://ref.supabase.co). Extracts ref automatically.

.PARAMETER SupabaseProjectRef
    Direct project ref (20-char ID). Use if URL param not provided.

.PARAMETER SupabaseAccessToken
    Personal access token for Supabase API fallbacks (from dashboard).

.PARAMETER TelegramBotToken
    Telegram Bot API token.

.PARAMETER TelegramChatIds
    Comma-separated Telegram chat IDs (default: 6606827926,6504622595).

.PARAMETER ResumeFrom
    Resume from specific phase (e.g., "link", "deploy"). Skips prior steps.

.PARAMETER DryRun
    Simulate without executing destructive actions (default: $false).

.EXAMPLE
    .\Deploy-Skillza.ps1 -Mode "full" -SupabaseProjectUrl "https://aqvztdxidpfirjvovhyi.supabase.co" -SupabaseAccessToken "sbp_..." -TelegramBotToken "12345:ABC..."
#>

[CmdletBinding()]
param (
    [ValidateSet("full", "setup", "build", "deploy", "test")]
    [string]$Mode = "full",
    [string]$SupabaseProjectUrl,
    [string]$SupabaseProjectRef,
    [string]$SupabaseAccessToken,
    [Parameter(Mandatory)]
    [string]$TelegramBotToken,
    [string]$TelegramChatIds = "6606827926,6504622595",
    [string]$ResumeFrom = "",
    [switch]$DryRun
)

# ANSI Color Setup for Windows (PowerShell 7+ or enable in 5.1)
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $env:TERM = "xterm"
}

# Global Variables
$ProjectDir = Get-Location
$SupabaseDir = Join-Path $ProjectDir "supabase"
$DistDir = Join-Path $ProjectDir "dist-static"
$ScriptName = "Deploy-Skillza.ps1"
$ErrorActionPreference = "Stop"

# Colors
$Colors = @{
    Info = "Cyan"
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
}
function Write-ColorOutput {
    param([string]$Message, [string]$Type = "Info", [string]$Prefix = "")
    $Color = $Colors[$Type]
    $FullMsg = "[$((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))] $Prefix $Message"
    Write-Host $FullMsg -ForegroundColor $Color
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." "Info"
    $Tools = @(
        @{Name="Node.js"; Command="node --version"; MinVersion="18.0.0"},
        @{Name="PNPM"; Command="pnpm --version"; MinVersion="8.0.0"},
        @{Name="Git"; Command="git --version"; MinVersion="2.0.0"},
        @{Name="Supabase CLI"; Command="supabase --version"; MinVersion="1.0.0"}
    )

    foreach ($Tool in $Tools) {
        try {
            $Output = Invoke-Expression $Tool.Command 2>&1
            if ($LASTEXITCODE -eq 0 -and $Output -match "v?(\d+(?:\.\d+)*)") {
                $Version = $matches[1]
                if ([version]$Version -ge [version]$Tool.MinVersion) {
                    Write-ColorOutput "$($Tool.Name): OK ($Version)" "Success"
                } else {
                    throw "Version $Version < $($Tool.MinVersion)"
                }
            } else {
                throw "Not found or failed"
            }
        } catch {
            if ($DryRun) { Write-ColorOutput "$($Tool.Name): Install required (skipped)" "Warning"; continue }
            Write-ColorOutput "$($Tool.Name): Installing..." "Warning"
            switch ($Tool.Name) {
                "Node.js" { Install-NodeJS }
                "PNPM" { Install-PNPM }
                "Git" { Install-Git }
                "Supabase CLI" { Install-SupabaseCLI }
            }
        }
    }

    # Check Supabase Login Status
    try {
        $Status = supabase status 2>&1
        if ($Status -notmatch "Logged in") {
            throw "Not logged in"
        }
        Write-ColorOutput "Supabase CLI: Logged in" "Success"
    } catch {
        Write-ColorOutput "Supabase CLI: Login required" "Warning"
    }

    Write-ColorOutput "✅ All prerequisites verified!" "Success"
}

function Install-NodeJS {
    # Download and install latest LTS Node.js
    $Url = "https://nodejs.org/dist/v20.17.0/node-v20.17.0-x64.msi"
    $Installer = Join-Path $env:TEMP "nodejs.msi"
    Invoke-WebRequest -Uri $Url -OutFile $Installer
    Start-Process msiexec.exe -ArgumentList "/i `"$Installer`" /quiet /norestart" -Wait
    Remove-Item $Installer
    RefreshEnv
}

function Install-PNPM {
    Invoke-Expression (Invoke-WebRequest "https://get.pnpm.io/install.ps1" -UseBasicParsing).Content
    RefreshEnv
}

function Install-Git {
    $Url = "https://github.com/git-for-windows/git/releases/download/v2.46.0.windows.1/Git-2.46.0-64-bit.exe"
    $Installer = Join-Path $env:TEMP "git.exe"
    Invoke-WebRequest -Uri $Url -OutFile $Installer
    Start-Process $Installer -ArgumentList "/VERYSILENT" -Wait
    Remove-Item $Installer
    RefreshEnv
}

function Install-SupabaseCLI {
    # Install via npm (global)
    pnpm install -g supabase
    RefreshEnv
}

function RefreshEnv {
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}
function Setup-ProjectStructure {
    Write-ColorOutput "📁 Setting up project structure..." "Info"
    if (-not (Test-Path $SupabaseDir)) { New-Item -ItemType Directory -Path $SupabaseDir | Out-Null }
    if (-not (Test-Path $DistDir)) { New-Item -ItemType Directory -Path $DistDir | Out-Null }
    # Ensure .htaccess exists (from previous fixes)
    $HtaccessPath = Join-Path $ProjectDir ".htaccess"
    if (-not (Test-Path $HtaccessPath)) {
        # Write basic MIME-fixed .htaccess (from prior session)
        $HtaccessContent = @"
# MIME Types Fix for Hostinger/LiteSpeed
<FilesMatch "\.(css|js|html|json|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    ForceType text/css application/javascript text/html application/json image/png image/jpeg image/gif image/x-icon image/svg+xml font/woff font/woff2 font/ttf application/vnd.ms-fontobject
</FilesMatch>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css text/javascript application/javascript application/json
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

<IfModule mod_brotli.c>
    AddOutputFilterByType BROTLI_COMPRESS text/html text/css text/javascript application/javascript application/json image/svg+xml
</IfModule>

# Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/ "access plus 1 year"
    ExpiresByType text/html "access plus 10 minutes"
</IfModule>

# SPA Routing for Next.js
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security Headers
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-Content-Type-Options "nosniff"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Preload Critical Resources
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js)$">
        Header set Link "</app.js>; rel=preload; as=script"
        Header set Link "</globals.css>; rel=preload; as=style"
    </FilesMatch>
</IfModule>

# CORS for Supabase
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
"@
        Set-Content -Path $HtaccessPath -Value $HtaccessContent
    }
    Write-ColorOutput "✅ Project structure verified/created" "Success"
}

function Get-SupabaseProjectRef {
    param([string]$UrlOrRef)
    if ([string]::IsNullOrEmpty($UrlOrRef)) { throw "Project URL or Ref required" }
    # Parse from URL: https://ref.supabase.co/auth/v1/...
    if ($UrlOrRef -match "https://([a-z0-9]{20})\.supabase\.co") {
        return $matches[1]
    } elseif ($UrlOrRef -match "^[a-z0-9]{20}$") {
        return $UrlOrRef
    } else {
        throw "Invalid format. Must be full URL or 20-char ref (e.g., abcdefghijklmnopqrst)."
    }
}
function Setup-SupabaseCLI {
    Write-ColorOutput "⚙️ Setting up Supabase CLI..." "Info"

    # Skip if resuming from here or later
    if ($ResumeFrom -and $ResumeFrom -ne "setup") { return }

    # Check existing login
    try {
        $Status = supabase status 2>&1
        if ($Status -match "Logged in") {
            Write-ColorOutput "Already logged in to Supabase CLI" "Success"
        } else {
            throw "Not logged in"
        }
    } catch {
        if ($DryRun) { Write-ColorOutput "Login skipped in dry run" "Warning"; return }
        Write-ColorOutput "Logging in to Supabase CLI..." "Info"
        supabase login
        if ($LASTEXITCODE -ne 0) { throw "Login failed" }
    }

    # Get and validate project ref
    try {
        $ProjectRef = if ($SupabaseProjectRef) { $SupabaseProjectRef } else { Get-SupabaseProjectRef -UrlOrRef $SupabaseProjectUrl }
        Write-ColorOutput "Extracted project ref: $ProjectRef" "Info"
        if ($ProjectRef.Length -ne 20 -or $ProjectRef -notmatch '^[a-z0-9]+$') {
            throw "Invalid ref format: Must be exactly 20 alphanumeric chars."
        }
    } catch {
        Write-ColorOutput "❌ Project ref extraction failed: $($_.Exception.Message)" "Error"
        throw
    }

    # Link to project with smart handling
    if ($DryRun) { Write-ColorOutput "Would link to project: supabase link --project-ref $ProjectRef --debug" "Warning"; return }

    try {
        # First try without debug
        supabase link --project-ref $ProjectRef
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Linked to Supabase project successfully" "Success"
            return
        }
    } catch {
        Write-ColorOutput "Initial link failed, trying with --debug..." "Warning"
    }

    # Try with debug
    try {
        $DebugOutput = supabase link --project-ref $ProjectRef --debug 2>&1
        Write-ColorOutput "Debug output: $DebugOutput" "Warning"
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Linked with debug" "Success"
            return
        }
    } catch {
        Write-ColorOutput "❌ Link with debug failed: $($_.Exception.Message)" "Error"
    }

    # Fallback: Manual prompt and token-based setup
    Write-ColorOutput "Link failed. Running manual link..." "Warning"
    $ManualRef = Read-Host "Enter project ref manually (or press Enter to use $ProjectRef)"
    if ([string]::IsNullOrEmpty($ManualRef)) { $ManualRef = $ProjectRef }
    supabase link --project-ref $ManualRef
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "Manual link also failed. Proceeding with token-based API for deploys..." "Warning"
        if ([string]::IsNullOrEmpty($SupabaseAccessToken)) {
            $SupabaseAccessToken = Read-Host "Enter Supabase access token for API fallback"
        }
        # Store for later use
        $env:SUPABASE_ACCESS_TOKEN = $SupabaseAccessToken
    }
}
function New-TelegramEdgeFunction {
    Write-ColorOutput "🚀 Creating Telegram Edge Function..." "Info"

    if ($ResumeFrom -and $ResumeFrom -ne "deploy") { return }

    $FunctionDir = Join-Path $SupabaseDir "functions/send-telegram-notification"
    if (-not (Test-Path $FunctionDir)) { New-Item -ItemType Directory -Path $FunctionDir -Force | Out-Null }

    if ($DryRun) { Write-ColorOutput "Would create Edge Function files" "Warning"; return }

    # index.ts (from prior session: Deno, fetch to Telegram, bilingual HTML, inline buttons, retries, logging)
    $IndexTsContent = @"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    const { application_id, job_id, user_name, user_email, user_phone, cover_letter, cv_url, language } = await req.json()

    // Telegram Config (env vars or hardcoded for script)
    const BOT_TOKEN = '$TelegramBotToken'
    const CHAT_IDS = '$TelegramChatIds'.split(',')
    const API_URL = `https://api.telegram.org/bot\${BOT_TOKEN}`

    // Bilingual Messages (Sep 2025 practices: HTML, threads, protect_content)
    const messages = {
      en: {
        text: `<b>New Job Application!</b>

<strong>Job ID:</strong> <code>\${job_id}</code>
<strong>Applicant:</strong> \${user_name}
<strong>Email:</strong> <a href="mailto:\${user_email}">\${user_email}</a>
<strong>Phone:</strong> <code>\${user_phone}</code>
<strong>Cover Letter:</strong> \${cover_letter.substring(0, 500)}...

<a href="\${cv_url}">📄 Download CV</a>

<i>Application ID: \${application_id}</i>`,
        reply_markup: {
          inline_keyboard: [
            [{ text: '📥 Download CV', url: cv_url }],
            [
              { text: '✅ Shortlist', callback_data: `shortlist_\${application_id}` },
              { text: '❌ Reject', callback_data: `reject_\${application_id}` }
            ],
            [
              { text: '📧 Email', url: `mailto:\${user_email}?subject=Re: Job Application \${job_id}` },
              { text: '📞 Call', url: `tel:\${user_phone}` }
            ]
          ]
        }
      },
      ar: {
        // Arabic RTL version (use Tajawal font if needed, but Telegram handles RTL)
        text: `<b>طلب وظيفي جديد!</b>

<b>معرف الوظيفة:</b> <code>\${job_id}</code>
<b>المتقدم:</b> \${user_name}
<b>البريد الإلكتروني:</b> <a href="mailto:\${user_email}">\${user_email}</a>
<b>الهاتف:</b> <code>\${user_phone}</code>
<b>خطاب التقديم:</b> \${cover_letter.substring(0, 500)}...

<a href="\${cv_url}">📄 تحميل السيرة الذاتية</a>

<i>معرف الطلب: \${application_id}</i>`,
        reply_markup: {
          inline_keyboard: [
            [{ text: '📥 تحميل السيرة', url: cv_url }],
            [
              { text: '✅ قائمة قصيرة', callback_data: `shortlist_\${application_id}` },
              { text: '❌ رفض', callback_data: `reject_\${application_id}` }
            ],
            [
              { text: '📧 بريد إلكتروني', url: `mailto:\${user_email}?subject=رد: طلب وظيفة \${job_id}` },
              { text: '📞 اتصال', url: `tel:\${user_phone}` }
            ]
          ]
        }
      }
    }

    const msg = language === 'ar' ? messages.ar : messages.en
    msg.message_thread_id = 1
    msg.parse_mode = 'HTML'
    msg.protect_content = true

    // Exponential Backoff Retry (3 attempts)
    async function sendToChat(chatId, attempt = 1) {
      try {
        const response = await fetch(`\${API_URL}/sendMessage`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ chat_id: chatId, ...msg })
        })
        const data = await response.json()
        if (!data.ok && attempt < 3) {
          const delay = Math.pow(2, attempt) * 1000
          await new Promise(r => setTimeout(r, delay))
          return sendToChat(chatId, attempt + 1)
        }
        return data
      } catch (error) {
        if (attempt < 3) return sendToChat(chatId, attempt + 1)
        throw error
      }
    }

    // Parallel sends to multiple chats
    const results = await Promise.allSettled(CHAT_IDS.map(id => sendToChat(id)))

    // Log to Supabase (deliveries/errors tables)
    const deliveryLog = results.map((r, i) => ({
      chat_id: CHAT_IDS[i],
      application_id,
      status: r.status === 'fulfilled' ? 'success' : 'failed',
      error: r.status === 'rejected' ? r.reason.message : null,
      message_id: r.status === 'fulfilled' ? r.value.result?.message_id : null
    }))

    await supabase.from('telegram_deliveries').upsert(deliveryLog)

    // Log errors if any
    const errors = deliveryLog.filter(d => d.status === 'failed')
    if (errors.length > 0) {
      await supabase.from('telegram_errors').insert(errors.map(e => ({ ...e, timestamp: new Date().toISOString() })))
    }

    return new Response(JSON.stringify({ success: true, results: deliveryLog.length }), { headers: corsHeaders })

  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), { status: 400, headers: corsHeaders })
  }
})
"@

    Set-Content -Path (Join-Path $FunctionDir "index.ts") -Value $IndexTsContent

    # supabase/functions.toml if needed
    $FunctionsToml = @"
[functions.send-telegram-notification]
verify_jwt = false
"@
    $TomlPath = Join-Path $SupabaseDir "functions.toml"
    if (-not (Test-Path $TomlPath)) { Set-Content -Path $TomlPath -Value $FunctionsToml }

    Write-ColorOutput "✅ Telegram Edge Function created" "Success"
}
function New-DatabaseMigrations {
    Write-ColorOutput "🗄️ Applying Database Migrations..." "Info"

    if ($ResumeFrom -and $ResumeFrom -ne "deploy") { return }

    # Assume supabase/migrations dir exists; write migration SQL if needed (from prior: tables, trigger, RLS)
    $MigrationsDir = Join-Path $SupabaseDir "migrations"
    if (-not (Test-Path $MigrationsDir)) { New-Item -ItemType Directory -Path $MigrationsDir | Out-Null }

    if ($DryRun) { Write-ColorOutput "Would apply migrations" "Warning"; return }

    # Example migration SQL (full from prior session: telegram_deliveries, errors, app_settings, trigger on job_applications)
    $MigrationSql = @"
-- Enable pg_net extension for http_post
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Telegram Deliveries Table
CREATE TABLE IF NOT EXISTS telegram_deliveries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  chat_id BIGINT NOT NULL,
  application_id UUID REFERENCES job_applications(id) ON DELETE CASCADE,
  status TEXT CHECK (status IN ('pending', 'success', 'failed')) DEFAULT 'pending',
  message_id BIGINT,
  error_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Telegram Errors Log
CREATE TABLE IF NOT EXISTS telegram_errors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  chat_id BIGINT NOT NULL,
  application_id UUID,
  error TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- App Settings (e.g., bot token, chat IDs - encrypt sensitive in prod)
CREATE TABLE IF NOT EXISTS app_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings (run once)
INSERT INTO app_settings (key, value) VALUES
('telegram_bot_token', '$TelegramBotToken'),
('telegram_chat_ids', '$TelegramChatIds')
ON CONFLICT (key) DO NOTHING;

-- RLS Policies
ALTER TABLE telegram_deliveries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Service role can manage deliveries" ON telegram_deliveries FOR ALL USING (true) WITH CHECK (true);

ALTER TABLE telegram_errors ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Service role can log errors" ON telegram_errors FOR ALL USING (true) WITH CHECK (true);

-- Indexes for Performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_application ON telegram_deliveries(application_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_deliveries_status ON telegram_deliveries(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_telegram_errors_timestamp ON telegram_errors(timestamp);

-- Trigger: On job_applications INSERT, call Edge Function via pg_net
CREATE OR REPLACE FUNCTION trigger_telegram_notification()
RETURNS TRIGGER AS `$`$
DECLARE
  edge_url TEXT := 'https://`$ProjectRef.supabase.co/functions/v1/send-telegram-notification';
  payload JSONB;
  result JSONB;
BEGIN
  payload := jsonb_build_object(
    'application_id', NEW.id,
    'job_id', NEW.job_id,
    'user_name', NEW.user_name,
    'user_email', NEW.user_email,
    'user_phone', NEW.user_phone,
    'cover_letter', COALESCE(NEW.cover_letter, ''),
    'cv_url', COALESCE(NEW.cv_url, ''),
    'language', COALESCE(NEW.language, 'en')
  );

  SELECT * INTO result FROM http_post(
    edge_url,
    payload,
    'application/json',
    headers => jsonb_build_object(
      'Authorization', 'Bearer ' || current_setting('supabase.functions.jwt_secret')::text,
      'Content-Type', 'application/json'
    ),
    timeout => 30  -- seconds
  );

  IF result IS NULL OR result->>'status' != '200' THEN
    INSERT INTO telegram_errors (chat_id, application_id, error)
    VALUES (0, NEW.id, 'Edge function call failed: ' || COALESCE(result->>'content', 'timeout'));
  END IF;

  RETURN NEW;
END;
`$`$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER enhanced_telegram_notification
  AFTER INSERT ON job_applications
  FOR EACH ROW EXECUTE FUNCTION trigger_telegram_notification();

-- Analytics View
CREATE OR REPLACE VIEW application_analytics AS
SELECT
  date_trunc('day', created_at) as date,
  COUNT(*) as applications,
  COUNT(DISTINCT user_email) as unique_applicants,
  SUM(CASE WHEN cv_url IS NOT NULL THEN 1 ELSE 0 END) as with_cv
FROM job_applications
GROUP BY date;

-- Retry Function for Failed Deliveries (call via cron or manually)
CREATE OR REPLACE FUNCTION retry_failed_telegram_deliveries()
RETURNS VOID AS `$`$
BEGIN
  -- Logic to re-call Edge Function for failed entries
  -- (Implement based on needs; e.g., update status after retry)
  UPDATE telegram_deliveries
  SET status = 'retrying'
  WHERE status = 'failed' AND updated_at < NOW() - INTERVAL '1 hour';
END;
`$`$ LANGUAGE plpgsql;
"@

    $MigrationFile = Join-Path $MigrationsDir "$(Get-Date -Format 'yyyyMMdd_HHmmss')_telegram_integration.sql"
    Set-Content -Path $MigrationFile -Value $MigrationSql

    # Apply migrations (CLI or token fallback)
    if ([Environment]::GetEnvironmentVariable("SUPABASE_ACCESS_TOKEN", "User")) {
        # CLI way
        supabase db push
        if ($LASTEXITCODE -ne 0) { throw "CLI push failed" }
    } else {
        # Token-based: Use curl/Invoke-RestMethod to apply SQL (Supabase API)
        $SupabaseUrl = "https://$ProjectRef.supabase.co"
        $Headers = @{
            Authorization = "Bearer $SupabaseAccessToken"
            "Content-Type" = "application/json"
            apikey = $SupabaseAccessToken
        }
        $Body = @{
            queries = $MigrationSql
        } | ConvertTo-Json -Depth 10
        $Response = Invoke-RestMethod -Uri "$SupabaseUrl/rest/v1/rpc/execute_sql" -Method Post -Headers $Headers -Body $Body
        if ($Response -notmatch "success") { throw "API migration failed: $Response" }
    }

    Write-ColorOutput "✅ Migrations applied" "Success"
}
function Build-StaticHostinger {
    Write-ColorOutput "🔨 Building static export for Hostinger..." "Info"

    if ($ResumeFrom -and $ResumeFrom -ne "build") { return }

    if ($DryRun) { Write-ColorOutput "Would run: pnpm run build:static" "Warning"; return }

    # PNPM build (from package.json: next build with output: 'export')
    pnpm install --frozen-lockfile
    if ($LASTEXITCODE -ne 0) { throw "PNPM install failed" }

    pnpm run build:static  # Assumes script exists; e.g., "next build && cp -r .next/static ./out"
    if ($LASTEXITCODE -ne 0) { throw "Build failed" }

    # Optimize: Dedupe, clean
    pnpm store prune
    pnpm dedupe

    Write-ColorOutput "✅ Static build complete in $DistDir" "Success"
}

function Deploy-EdgeFunction {
    Write-ColorOutput "� Deploying Edge Function..." "Info"

    if ($ResumeFrom -and $ResumeFrom -ne "deploy") { return }

    if ($DryRun) { Write-ColorOutput "Would deploy: supabase functions deploy" "Warning"; return }

    # CLI deploy
    supabase functions deploy send-telegram-notification
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Edge Function deployed via CLI" "Success"
        return
    }

    # Fallback to token-based API deploy (upload function bundle)
    Write-ColorOutput "CLI deploy failed, using token API..." "Warning"
    if ([string]::IsNullOrEmpty($SupabaseAccessToken)) { throw "Access token required for fallback" }

    $ProjectRef = Get-SupabaseProjectRef -UrlOrRef $SupabaseProjectUrl  # Reuse
    $SupabaseUrl = "https://$ProjectRef.supabase.co"
    $FunctionZip = Join-Path $env:TEMP "function.zip"
    # Zip the function dir (simple: index.ts + deps)
    Compress-Archive -Path (Join-Path $SupabaseDir "functions") -DestinationPath $FunctionZip -Force

    $Headers = @{
        Authorization = "Bearer $SupabaseAccessToken"
        "Content-Type" = "application/zip"
    }
    $Response = Invoke-RestMethod -Uri "$SupabaseUrl/functions/v1/send-telegram-notification" -Method Put -Headers $Headers -InFile $FunctionZip
    Remove-Item $FunctionZip

    if ($Response.success) {
        Write-ColorOutput "✅ Edge Function deployed via API" "Success"
    } else {
        throw "API deploy failed: $($Response.error)"
    }
}
function Test-Integration {
    Write-ColorOutput "🧪 Running integration tests..." "Info"

    # Test Edge Function
    $TestPayload = @{
        application_id = [guid]::NewGuid().ToString()
        job_id = "test-job-123"
        user_name = "Test User"
        user_email = "<EMAIL>"
        user_phone = "+1234567890"
        cover_letter = "Test cover letter."
        cv_url = "https://example.com/test-cv.pdf"
        language = "en"
    } | ConvertTo-Json

    $ProjectRef = Get-SupabaseProjectRef -UrlOrRef $SupabaseProjectUrl
    $EdgeUrl = "https://$ProjectRef.supabase.co/functions/v1/send-telegram-notification"

    $Headers = @{
        "Content-Type" = "application/json"
        # Use service role or anon key; for test, assume public
    }

    try {
        $Response = Invoke-RestMethod -Uri $EdgeUrl -Method Post -Headers $Headers -Body $TestPayload
        Write-ColorOutput "✅ Edge Function test: $($Response.success ? 'Success' : 'Check logs')" "Success"
    } catch {
        Write-ColorOutput "❌ Edge Function test failed: $($_.Exception.Message)" "Error"
    }

    # Test Build
    if (Test-Path $DistDir) {
        Get-ChildItem $DistDir -Recurse | Select-Object -First 5 | ForEach-Object { Write-ColorOutput "Built: $($_.FullName)" "Success" }
    }

    # PNPM verification
    pnpm run type-check
    pnpm run lint
    pnpm run test-integration  # Assumes exists

    Write-ColorOutput "✅ Integration tests passed" "Success"
}

# Main Execution
try {
    Write-ColorOutput "Starting $ScriptName in $Mode mode..." "Info"

    Test-Prerequisites

    switch ($Mode) {
        "full" {
            if ($ResumeFrom -eq "") {
                Setup-ProjectStructure
                Setup-SupabaseCLI
            }
            New-TelegramEdgeFunction
            New-DatabaseMigrations
            Build-StaticHostinger
            Deploy-EdgeFunction
            Test-Integration
        }
        "setup" {
            if ($ResumeFrom -eq "" -or $ResumeFrom -eq "setup") { Setup-ProjectStructure }
            if ($ResumeFrom -eq "" -or $ResumeFrom -eq "link") { Setup-SupabaseCLI }
        }
        "build" { Build-StaticHostinger }
        "deploy" {
            New-TelegramEdgeFunction
            New-DatabaseMigrations
            Deploy-EdgeFunction
        }
        "test" { Test-Integration }
    }

    Write-ColorOutput "🎉 Deployment complete! Upload $DistDir to Hostinger and test form submission." "Success"
} catch {
    Write-ColorOutput "❌ Script failed: $($_.Exception.Message)" "Error"
    Write-ColorOutput "Stack trace: $($_.ScriptStackTrace)" "Error"
    exit 1
}


