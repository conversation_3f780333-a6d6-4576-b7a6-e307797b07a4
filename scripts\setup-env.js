#!/usr/bin/env node

/**
 * Environment Setup Script
 * 
 * Creates a .env file with the required configuration
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function createEnvFile() {
  const envPath = path.join(process.cwd(), '.env');
  
  // Check if .env already exists
  if (fs.existsSync(envPath)) {
    log(`${colors.yellow}⚠️ .env file already exists${colors.reset}`, 'yellow');
    log(`${colors.cyan}Backing up existing .env to .env.backup${colors.reset}`, 'cyan');
    fs.copyFileSync(envPath, path.join(process.cwd(), '.env.backup'));
  }
  
  const envContent = `# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://aqvztdxidpfirjvovhyi.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFxdnp0ZHhpZHBmaXJqdm92aHlpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgwNDkwMjIsImV4cCI6MjA3MzYyNTAyMn0.BzTYN8hc2h4Q8p0E55dM0vBcNpVjnds9h_H1yYRpWns

# Telegram Bot Configuration (Required)
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_IDS=6606827926,6504622595

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://skillzajobs.com

# Google Analytics 4 (Optional - leave empty to disable)
# NEXT_PUBLIC_GA_MEASUREMENT_ID=G-03XW3FWG7L
`;

  try {
    fs.writeFileSync(envPath, envContent);
    log(`${colors.green}✅ .env file created successfully${colors.reset}`);
    log(`${colors.cyan}📝 You can now run the deployment script${colors.reset}`);
    return true;
  } catch (error) {
    log(`${colors.red}❌ Failed to create .env file: ${error.message}${colors.reset}`, 'red');
    return false;
  }
}

function main() {
  log(`${colors.bright}${colors.blue}🔧 Environment Setup${colors.reset}`);
  log(`${colors.blue}===================${colors.reset}`);
  
  if (createEnvFile()) {
    log(`\n${colors.green}🎉 Environment setup completed!${colors.reset}`);
    log(`\n${colors.cyan}Next steps:${colors.reset}`);
    log(`1. Review the .env file and update values if needed`);
    log(`2. Run: node scripts/deploy-telegram-integration.js`);
    log(`3. Test the application form submission`);
  } else {
    log(`\n${colors.red}❌ Environment setup failed${colors.reset}`, 'red');
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = { main };
