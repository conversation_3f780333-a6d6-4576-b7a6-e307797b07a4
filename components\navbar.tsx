"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/lib/language-context"
import { Menu, X, Home, Briefcase, Users, Mail, Globe } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function Navbar() {
  const { language, isRTL, setLanguage } = useLanguage()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navItems = [
    { 
      name: language === "ar" ? "الرئيسية" : "Home", 
      href: "/", 
      icon: <Home size={18} /> 
    },
    { 
      name: language === "ar" ? "الوظائف" : "Jobs", 
      href: "/jobs", 
      icon: <Briefcase size={18} /> 
    },
    { 
      name: language === "ar" ? "من نحن" : "About", 
      href: "/about", 
      icon: <Users size={18} /> 
    },
    { 
      name: language === "ar" ? "اتصل بنا" : "Contact", 
      href: "/contact", 
      icon: <Mail size={18} /> 
    }
  ]

  // Close mobile menu when clicking outside or on route change
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (isOpen && event.target instanceof Element) {
        const mobileMenu = document.querySelector('[data-mobile-menu]')
        const menuButton = document.querySelector('[data-menu-button]')
        if (mobileMenu && !mobileMenu.contains(event.target) && 
            menuButton && !menuButton.contains(event.target)) {
          setIsOpen(false)
        }
      }
    }
    
    if (isOpen) {
      document.addEventListener("pointerdown", handleClickOutside)
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
    
    return () => {
      document.removeEventListener("pointerdown", handleClickOutside)
      document.body.style.overflow = ''
    }
  }, [isOpen])

  // Close mobile menu on route changes
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? "bg-white/90 backdrop-blur-md shadow-md py-2" : "bg-white/90 backdrop-blur-sm py-4"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between w-full">
          {/* Logo */}
          <Link 
            href="/" 
            className="flex items-center gap-2"
            onClick={() => setIsOpen(false)}
          >
            <div className="bg-indigo-600 text-white font-bold text-xl w-10 h-10 rounded-lg flex items-center justify-center">
              S
            </div>
            <span className={`text-xl font-bold text-slate-900 ${isRTL ? "font-arabic" : "font-sans"}`}>
              {language === "ar" ? "سكيلزا" : "Skillza"}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center flex-grow justify-center" aria-label="Main navigation">
            <div className="flex items-center justify-center flex-grow">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2 ${
                    pathname === item.href
                      ? "bg-indigo-100 text-indigo-700"
                      : "text-slate-900 hover:bg-slate-100"
                  } font-arabic`}
                  aria-current={pathname === item.href ? 'page' : undefined}
                >
                  <>{item.icon}<span>{item.name}</span></>
                </Link>
              ))}
            </div>

            <Link href="/apply">
              <Button size="sm" className="mr-4 font-arabic">
                {language === "ar" ? "تقدم الآن" : "Apply Now"}
              </Button>
            </Link>
          </nav>

          {/* Mobile Navigation and Language Switcher */}
          <div className="flex items-center gap-2">
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-slate-900">
                  <Globe size={20} />
                  <span className="sr-only">Toggle language</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setLanguage("en")}>
                  English
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage("ar")}>
                  العربية
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile Navigation Button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation()
                  setIsOpen(!isOpen)
                }}
                className="text-slate-900"
                aria-expanded={isOpen}
                aria-controls="mobile-menu"
                aria-label={isOpen ? "Close menu" : "Open menu"}
                data-menu-button
              >
                {isOpen ? <X size={24} /> : <Menu size={24} />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div 
          id="mobile-menu"
          className={`md:hidden mt-4 py-4 border-t border-slate-200 bg-white rounded-lg shadow-lg absolute left-4 right-4 z-50 transition-transform duration-300 ease-in-out ${isOpen ? 'transform translate-y-0' : 'transform -translate-y-[150%]'}`}
          onClick={(e) => e.stopPropagation()}
          data-mobile-menu
        >
          <nav className="flex flex-col gap-2">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => setIsOpen(false)}
                className={`px-4 py-3 rounded-lg text-sm font-medium transition-colors flex items-center gap-3 ${
                  pathname === item.href
                    ? "bg-indigo-100 text-indigo-700"
                    : "text-slate-700 hover:bg-slate-100"
                } font-arabic`}
              >
                <>{item.icon}<span>{item.name}</span></>
              </Link>
            ))}
            
            <Link href="/apply" onClick={() => setIsOpen(false)}>
              <Button className="w-full mt-2 font-arabic">
                {language === "ar" ? "تقدم الآن" : "Apply Now"}
              </Button>
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}
