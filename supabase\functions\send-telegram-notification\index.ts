import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const telegramBotToken = Deno.env.get('TELEGRAM_BOT_TOKEN')
const telegramChatIds = Deno.env.get('TELEGRAM_CHAT_IDS')?.split(',').map(id => id.trim()).filter(Boolean)

console.log('Telegram Bot Token:', telegramBotToken ? 'Set' : 'Not set')
console.log('Telegram Chat IDs:', telegramChatIds?.length || 0)

if (!telegramBotToken || !telegramChatIds || telegramChatIds.length === 0) {
  console.error('Missing Telegram configuration')
  throw new Error('Telegram configuration is incomplete')
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}


// Enhanced Telegram API helper with retry logic
async function sendTelegramMessage(chatId: string, text: string, options: any = {}) {
  const maxRetries = 3
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`https://api.telegram.org/bot${telegramBotToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId,
          text,
          ...options
        })
      })

      const result = await response.json()
      
      if (result.ok) {
        return result
      } else if (result.error_code === 429 && attempt < maxRetries) {
        // Rate limited, wait and retry
        const retryAfter = result.parameters?.retry_after || 1
        console.log(`Rate limited, waiting ${retryAfter}s before retry ${attempt + 1}`)
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000))
        continue
      } else {
        throw new Error(result.description || 'Unknown Telegram API error')
      }
    } catch (error) {
      lastError = error
      if (attempt < maxRetries) {
        console.log(`Attempt ${attempt} failed, retrying...`, error)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
  }
  
  throw lastError
}

async function sendTelegramDocument(chatId: string, document: string, caption: string, options: any = {}) {
  const maxRetries = 3
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`https://api.telegram.org/bot${telegramBotToken}/sendDocument`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId,
          document,
          caption,
          ...options
        })
      })

      const result = await response.json()
      
      if (result.ok) {
        return result
      } else if (result.error_code === 429 && attempt < maxRetries) {
        const retryAfter = result.parameters?.retry_after || 1
        console.log(`Rate limited, waiting ${retryAfter}s before retry ${attempt + 1}`)
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000))
        continue
      } else {
        throw new Error(result.description || 'Unknown Telegram API error')
      }
    } catch (error) {
      lastError = error
      if (attempt < maxRetries) {
        console.log(`Document send attempt ${attempt} failed, retrying...`, error)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
  }
  
  throw lastError
}

serve(async (req) => {
  console.log('Received request:', req.method, req.url)
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  
  try {
    const { record } = await req.json()
    console.log('Application record:', record)
    
    const { name, email, phone, position, cv_url, experience, language = 'en' } = record

    // Enhanced message with better formatting and localization
    const isArabic = language === 'ar'
    const timestamp = new Date().toLocaleString(isArabic ? 'ar-SA' : 'en-US', { 
      timeZone: 'Asia/Riyadh',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })

    const message = `🔔 *${isArabic ? 'طلب توظيف جديد' : 'New Job Application'}*

👤 *${isArabic ? 'الاسم' : 'Name'}:* ${name}
📧 *${isArabic ? 'البريد الإلكتروني' : 'Email'}:* ${email}
📱 *${isArabic ? 'الهاتف' : 'Phone'}:* ${phone}
💼 *${isArabic ? 'المنصب' : 'Position'}:* ${position}
🎯 *${isArabic ? 'الخبرة' : 'Experience'}:* ${experience}
📄 *${isArabic ? 'السيرة الذاتية' : 'CV'}:* ${cv_url}

_${isArabic ? 'تم الإرسال في' : 'Submitted at'}: ${timestamp}_`

    console.log('Sending message to chats:', telegramChatIds)

    // Send to all chat IDs with enhanced error handling
    const results = []
    const sendPromises = telegramChatIds.map(async (chatId: string) => {
      try {
        console.log(`Sending to chat ${chatId}`)
        
        // Send main message
        const messageResult = await sendTelegramMessage(chatId, message, { 
          parse_mode: 'Markdown',
          disable_web_page_preview: true
        })
        
        const chatResult = { 
          chatId, 
          messageId: messageResult.result.message_id, 
          status: 'success',
          timestamp: new Date().toISOString()
        }
        
        // Send CV as document if available
        if (cv_url) {
          try {
            const docResult = await sendTelegramDocument(chatId, cv_url, 
              `📄 ${isArabic ? 'السيرة الذاتية لـ' : 'CV for'} ${name} - ${position}`, {
              reply_markup: {
                inline_keyboard: [[
                  { text: `📄 ${isArabic ? 'عرض السيرة الذاتية' : 'View CV'}`, url: cv_url }
                ]]
              }
            })
            chatResult.documentId = docResult.result.message_id
          } catch (docError) {
            console.error(`Failed to send document to chat ${chatId}:`, docError)
            chatResult.documentError = docError.message
            chatResult.status = 'partial'
          }
        }
        
        results.push(chatResult)
        return chatResult
        
      } catch (error) {
        console.error(`Failed to send to chat ${chatId}:`, error)
        const errorResult = { 
          chatId, 
          error: error.message, 
          status: 'error',
          timestamp: new Date().toISOString()
        }
        results.push(errorResult)
        return errorResult
      }
    })

    // Wait for all messages to be sent
    await Promise.allSettled(sendPromises)

    const successCount = results.filter(r => r.status === 'success' || r.status === 'partial').length
    const totalCount = results.length

    console.log('Notification results:', results)
    console.log(`Successfully sent to ${successCount}/${totalCount} chats`)

    return new Response(JSON.stringify({ 
      success: successCount > 0, 
      results,
      successCount,
      totalCount,
      message: `Notifications sent to ${successCount}/${totalCount} chats successfully` 
    }), { 
      status: successCount > 0 ? 200 : 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error sending notification:', error)
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message,
      timestamp: new Date().toISOString()
    }), { 
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
