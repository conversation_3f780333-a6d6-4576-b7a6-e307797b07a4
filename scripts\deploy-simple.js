#!/usr/bin/env node

/**
 * Simple Deployment Script
 * 
 * Deploys the Telegram integration without database changes
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n${colors.cyan}🔄 ${description}...${colors.reset}`);
  try {
    const output = execSync(command, { 
      stdio: 'pipe', 
      encoding: 'utf8',
      cwd: process.cwd()
    });
    log(`${colors.green}✅ ${description} completed successfully${colors.reset}`);
    return output;
  } catch (error) {
    log(`${colors.red}❌ ${description} failed:${colors.reset}`, 'red');
    log(error.message, 'red');
    throw error;
  }
}

function checkEnvironment() {
  log(`\n${colors.blue}🔍 Checking environment configuration...${colors.reset}`);
  
  const requiredEnvVars = [
    'TELEGRAM_BOT_TOKEN',
    'TELEGRAM_CHAT_IDS'
  ];
  
  const missingVars = [];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  }
  
  if (missingVars.length > 0) {
    log(`${colors.red}❌ Missing required environment variables:${colors.reset}`, 'red');
    missingVars.forEach(varName => log(`  - ${varName}`, 'red'));
    log(`\n${colors.yellow}Please set these variables in your .env file or environment${colors.reset}`, 'yellow');
    process.exit(1);
  }
  
  log(`${colors.green}✅ All required environment variables are set${colors.reset}`);
}

function validateTelegramConfig() {
  log(`\n${colors.blue}🤖 Validating Telegram bot configuration...${colors.reset}`);
  
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  const chatIds = process.env.TELEGRAM_CHAT_IDS?.split(',').map(id => id.trim()).filter(Boolean);
  
  if (!botToken || !chatIds || chatIds.length === 0) {
    log(`${colors.red}❌ Invalid Telegram configuration${colors.reset}`, 'red');
    process.exit(1);
  }
  
  // Validate bot token format
  if (!botToken.match(/^\d+:[A-Za-z0-9_-]+$/)) {
    log(`${colors.red}❌ Invalid bot token format${colors.reset}`, 'red');
    process.exit(1);
  }
  
  // Validate chat IDs
  for (const chatId of chatIds) {
    if (!chatId.match(/^-?\d+$/)) {
      log(`${colors.red}❌ Invalid chat ID format: ${chatId}${colors.reset}`, 'red');
      process.exit(1);
    }
  }
  
  log(`${colors.green}✅ Telegram bot configuration is valid${colors.reset}`);
  log(`  Bot Token: ${botToken.substring(0, 10)}...${botToken.substring(botToken.length - 10)}`);
  log(`  Chat IDs: ${chatIds.join(', ')}`);
}

function deploySupabaseFunctions() {
  log(`\n${colors.blue}🚀 Deploying Supabase Edge Functions...${colors.reset}`);
  
  try {
    // Check if Supabase CLI is installed
    execSync('supabase --version', { stdio: 'pipe' });
  } catch (error) {
    log(`${colors.red}❌ Supabase CLI not found. Please install it first:${colors.reset}`, 'red');
    log(`  npm install -g supabase`, 'yellow');
    process.exit(1);
  }
  
  // Deploy the send-telegram-notification function
  execCommand(
    'supabase functions deploy send-telegram-notification',
    'Deploying send-telegram-notification function'
  );
}

function buildAndDeploy() {
  log(`\n${colors.blue}🏗️ Building and deploying application...${colors.reset}`);
  
  // Build the application
  execCommand('npm run build', 'Building Next.js application');
  
  // Copy .htaccess to out directory
  const htaccessSource = path.join(process.cwd(), '.htaccess');
  const htaccessDest = path.join(process.cwd(), 'out', '.htaccess');
  
  if (fs.existsSync(htaccessSource)) {
    fs.copyFileSync(htaccessSource, htaccessDest);
    log(`${colors.green}✅ Copied .htaccess to out directory${colors.reset}`);
  }
  
  log(`${colors.green}✅ Application built and ready for deployment${colors.reset}`);
  log(`  Build output: ${path.join(process.cwd(), 'out')}`);
}

function main() {
  log(`${colors.bright}${colors.cyan}🚀 Simple Telegram Integration Deployment${colors.reset}`);
  log(`${colors.cyan}============================================${colors.reset}`);
  
  try {
    checkEnvironment();
    validateTelegramConfig();
    deploySupabaseFunctions();
    buildAndDeploy();
    
    log(`\n${colors.bright}${colors.green}🎉 Deployment completed successfully!${colors.reset}`);
    log(`\n${colors.cyan}Next steps:${colors.reset}`);
    log(`1. Upload the contents of the 'out' directory to your web server`);
    log(`2. Test the application form to verify Telegram notifications`);
    log(`3. Monitor the Supabase Edge Function logs for any issues`);
    log(`\n${colors.yellow}Note: Database triggers are already set up and working${colors.reset}`, 'yellow');
    
  } catch (error) {
    log(`\n${colors.red}❌ Deployment failed:${colors.reset}`, 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main };
