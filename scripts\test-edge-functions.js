#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🧪 Testing Supabase Edge Functions...');

try {
  // Test if functions are deployed
  console.log('📋 Checking deployed functions...');
  const output = execSync('supabase functions list', { encoding: 'utf8' });
  console.log(output);
  
  // Check if environment variables are set
  console.log('\n🔧 Checking environment variables...');
  const envOutput = execSync('supabase secrets list', { encoding: 'utf8' });
  console.log(envOutput);
  
  // Test the send-telegram-notification function
  console.log('\n📡 Testing send-telegram-notification function...');
  const testPayload = {
    record: {
      id: 'test-123',
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+966501234567',
      position: 'Test Position',
      experience: '2-5 years',
      cv_url: 'https://example.com/test.pdf',
      language: 'en',
      submitted_at: new Date().toISOString()
    }
  };
  
  try {
    // Get the anon key from environment or use a placeholder
    const anonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFxdnp0ZHhpZHBmaXJqdm92aHlpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1NzQ4MDAsImV4cCI6MjA1MDE1MDgwMH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
    
    // Use curl to test the function with proper authorization
    const testUrl = 'https://aqvztdxidpfirjvovhyi.functions.supabase.co/send-telegram-notification';
    const testOutput = execSync(`curl -X POST "${testUrl}" -H "Content-Type: application/json" -H "Authorization: Bearer ${anonKey}" -d '${JSON.stringify(testPayload)}'`, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log('✅ Function test successful:', testOutput);
  } catch (testError) {
    console.log('⚠️  Function test failed (this might be expected if Telegram is not configured):', testError.message);
  }
  
  console.log('\n🎉 Edge Functions testing completed!');
  
} catch (error) {
  console.error('❌ Testing failed:', error.message);
  process.exit(1);
}
