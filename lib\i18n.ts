export type Language = "ar" | "en"

export interface Translations {
  // Navigation & General
  skipToContent: string
  applyNow: string

  // Hero Section
  heroTitle: string
  heroSubtitle: string

  // About Section
  aboutTitle: string
  aboutDescription1: string
  aboutDescription2: string

  // Positions Section
  openPositions: string
  septemberIntake: string
  filterAll: string
  noPositionsFound: string
  applyForPosition: string

  // Form Section
  formTitle: string
  formSubtitle: string
  name: string
  email: string
  phone: string
  experience: string
  position: string
  uploadCV: string
  consent: string
  submit: string
  submitting: string

  // Experience Options
  experience0to2: string
  experience3to5: string
  experience6to10: string
  experience10plus: string

  // Placeholders
  enterFullName: string
  emailPlaceholder: string
  phonePlaceholder: string
  selectExperience: string
  selectPosition: string
  chooseFile: string

  // Messages
  fileTooLarge: string
  invalidFileType: string
  missingInfo: string
  applicationSubmitted: string
  applicationSubmittedDesc: string
  submissionFailed: string
  submissionFailedDesc: string
  maxFileSize: string

  // Validation
  validation: {
    nameRequired: string;
    emailInvalid: string;
    phoneInvalid: string;
    experienceRequired: string;
    positionRequired: string;
    cvRequired: string;
    consentRequired: string;
  };
  submissionSuccess: string;
  submissionSuccessDesc: string;
}

export const translations: Record<Language, Translations> = {
  ar: {
    // Navigation & General
    skipToContent: "انتقل إلى المحتوى الرئيسي",
    applyNow: "قدم الآن",

    // Hero Section
    heroTitle: "ابن مستقبلك مع سكيلزا",
    heroSubtitle:
      "شريكك المتخصص في التوظيف والموارد البشرية. نوفر أفضل الفرص الوظيفية في جميع القطاعات بالمملكة العربية السعودية.",

    // About Section
    aboutTitle: "حول سكيلزا",
    aboutDescription1:
      "سكيلزا هي شركة رائدة في مجال التوظيف والموارد البشرية، متخصصة في ربط المواهب المتميزة بأفضل الفرص الوظيفية. نقدم خدمات توظيف شاملة تغطي جميع القطاعات والتخصصات في المملكة العربية السعودية، مع التركيز على الجودة والاحترافية.",
    aboutDescription2:
      "بفضل خبرتنا الواسعة وشبكتنا القوية من الشركاء، نساعد الباحثين عن العمل في العثور على الوظائف المناسبة لمهاراتهم وطموحاتهم. كما نساعد الشركات في العثور على أفضل المواهب لتحقيق أهدافها. انضم إلينا واكتشف الفرص اللامحدودة في سوق العمل السعودي.",

    // Positions Section
    openPositions: "الوظائف المتاحة",
    septemberIntake: "دفعة سبتمبر 2025",
    filterAll: "الكل",
    noPositionsFound: "لا توجد وظائف للقطاع المحدد.",
    applyForPosition: "قدم لهذه الوظيفة",

    // Form Section
    formTitle: "قدم طلبك إلى سكيلزا",
    formSubtitle: "انضم إلى شبكة المواهب المتميزة",
    name: "الاسم *",
    email: "البريد الإلكتروني *",
    phone: "رقم التلفون *",
    experience: "الخبرة *",
    position: "اختيار الوظيفة *",
    uploadCV: "رفع السيرة الذاتية *",
    consent:
      "أوافق على معالجة بياناتي الشخصية وفقاً لسياسة الخصوصية الخاصة بشركة سكيلزا. أفهم أن بياناتي ستُستخدم لأغراض التوظيف فقط وسيتم التعامل معها بسرية تامة.",
    submit: "إرسال الطلب",
    submitting: "جاري الإرسال...",

    // Experience Options
    experience0to2: "0-2 سنوات",
    experience3to5: "3-5 سنوات",
    experience6to10: "6-10 سنوات",
    experience10plus: "أكثر من 10 سنوات",

    // Placeholders
    enterFullName: "أدخل اسمك الكامل",
    emailPlaceholder: "<EMAIL>",
    phonePlaceholder: "+966xxxxxxxxx",
    selectExperience: "اختر سنوات الخبرة",
    selectPosition: "اختر الوظيفة المرغوبة",
    chooseFile: "اختر ملف (PDF, DOC, DOCX)",

    // Messages
    fileTooLarge: "الملف كبير جداً",
    invalidFileType: "نوع ملف غير صالح",
    missingInfo: "معلومات مفقودة",
    applicationSubmitted: "تم إرسال الطلب!",
    applicationSubmittedDesc: "شكراً لاهتمامك بسكيلزا. سنراجع طلبك ونتواصل معك قريباً.",
    submissionFailed: "فشل في الإرسال",
    submissionFailedDesc: "حدث خطأ في إرسال طلبك. يرجى المحاولة مرة أخرى.",
    maxFileSize: "الحد الأقصى لحجم الملف: 5 ميجابايت",

    // Validation
    validation: {
      nameRequired: "الاسم مطلوب",
      emailInvalid: "بريد إلكتروني غير صالح",
      phoneInvalid: "رقم هاتف غير صالح",
      experienceRequired: "الخبرة مطلوبة",
      positionRequired: "الوظيفة مطلوبة",
      cvRequired: "السيرة الذاتية مطلوبة",
      consentRequired: "يجب الموافقة على الشروط",
    },
    submissionSuccess: "تم إرسال الطلب بنجاح",
    submissionSuccessDesc: "شكراً لتقديمك. سنتواصل معك قريباً.",
  },
  en: {
    // Navigation & General
    skipToContent: "Skip to main content",
    applyNow: "Apply Now",

    // Hero Section
    heroTitle: "Build your future with Skillza",
    heroSubtitle:
      "Your specialized partner in recruitment and human resources. We provide the best job opportunities across all sectors in Saudi Arabia.",

    // About Section
    aboutTitle: "About Skillza",
    aboutDescription1:
      "Skillza is a leading recruitment and human resources company, specializing in connecting exceptional talent with the best job opportunities. We provide comprehensive recruitment services covering all sectors and specializations in Saudi Arabia, with a focus on quality and professionalism.",
    aboutDescription2:
      "With our extensive experience and strong network of partners, we help job seekers find positions that match their skills and ambitions. We also help companies find the best talent to achieve their goals. Join us and discover unlimited opportunities in the Saudi job market.",

    // Positions Section
    openPositions: "Open Positions",
    septemberIntake: "September 2025 Intake",
    filterAll: "All",
    noPositionsFound: "No positions found for the selected sector.",
    applyForPosition: "Apply for this position",

    // Form Section
    formTitle: "Apply to Skillza",
    formSubtitle: "Join our network of exceptional talent",
    name: "Name *",
    email: "Email *",
    phone: "Phone *",
    experience: "Experience *",
    position: "Position *",
    uploadCV: "Upload CV *",
    consent:
      "I agree to the processing of my personal data in accordance with Skillza's privacy policy. I understand that my data will be used for recruitment purposes only and will be handled with complete confidentiality.",
    submit: "Submit Application",
    submitting: "Submitting...",

    // Experience Options
    experience0to2: "0-2 years",
    experience3to5: "3-5 years",
    experience6to10: "6-10 years",
    experience10plus: "10+ years",

    // Placeholders
    enterFullName: "Enter your full name",
    emailPlaceholder: "<EMAIL>",
    phonePlaceholder: "+966xxxxxxxxx",
    selectExperience: "Select years of experience",
    selectPosition: "Select desired position",
    chooseFile: "Choose file (PDF, DOC, DOCX)",

    // Messages
    fileTooLarge: "File too large",
    invalidFileType: "Invalid file type",
    missingInfo: "Missing information",
    applicationSubmitted: "Application submitted!",
    applicationSubmittedDesc:
      "Thank you for your interest in Skillza. We will review your application and get back to you soon.",
    submissionFailed: "Submission failed",
    submissionFailedDesc: "There was an error submitting your application. Please try again.",
    maxFileSize: "Maximum file size: 5MB",

    // Validation
    validation: {
      nameRequired: "Name is required",
      emailInvalid: "Invalid email address",
      phoneInvalid: "Invalid phone number",
      experienceRequired: "Experience is required",
      positionRequired: "Position is required",
      cvRequired: "CV is required",
      consentRequired: "You must agree to the terms",
    },
    submissionSuccess: "Application Submitted!",
    submissionSuccessDesc: "Thank you for applying. We will be in touch shortly.",
  },
}

export function getTranslations(lang: Language): Translations {
  return translations[lang]
}
