#!/usr/bin/env node

/**
 * Daily Job Date Update Script
 * This script can be run via cron job to update job dates daily
 * Usage: node scripts/daily-update.js
 */

const { updateJobDates, validateJobsData } = require('./update-job-dates');
const https = require('https');
const http = require('http');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000/api/update-job-dates';
const USE_API = process.env.USE_API === 'true';

/**
 * Make HTTP request to update job dates via API
 */
function updateViaAPI() {
  return new Promise((resolve, reject) => {
    const url = new URL(API_URL);
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Daily-Update-Script/1.0'
      }
    };

    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success) {
            console.log('✅ API update successful:', result.message);
            console.log(`📊 Updated ${result.updatedCount} jobs`);
            resolve(result);
          } else {
            reject(new Error(result.error || 'API update failed'));
          }
        } catch (error) {
          reject(new Error(`Failed to parse API response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`API request failed: ${error.message}`));
    });

    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('API request timeout'));
    });

    req.end();
  });
}

/**
 * Main update function
 */
async function dailyUpdate() {
  const startTime = new Date();
  console.log('🕐 Daily job date update started at:', startTime.toISOString());
  console.log('');

  try {
    if (USE_API) {
      console.log('🌐 Using API endpoint for update...');
      await updateViaAPI();
    } else {
      console.log('📁 Using direct file update...');
      updateJobDates();
    }

    console.log('');
    console.log('🔍 Validating updated data...');
    validateJobsData();

    const endTime = new Date();
    const duration = endTime - startTime;
    
    console.log('');
    console.log('✅ Daily update completed successfully!');
    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`🕐 Completed at: ${endTime.toISOString()}`);

  } catch (error) {
    console.error('❌ Daily update failed:', error.message);
    process.exit(1);
  }
}

// Run the update if this script is executed directly
if (require.main === module) {
  dailyUpdate();
}

module.exports = { dailyUpdate };
