'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Calendar, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react'

export default function UpdateDatesPage() {
  const [isUpdating, setIsUpdating] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<string | null>(null)
  const [updateResult, setUpdateResult] = useState<any>(null)
  const { toast } = useToast()

  const handleUpdateDates = async () => {
    // In production (static export), API routes are not available.
    // We update dates automatically during build and via CI.
    if (process.env.NODE_ENV !== 'development') {
      toast({
        title: 'Not available in production',
        description: 'Dates are updated automatically during build/CI. Use this button only in development.',
      })
      return
    }

    setIsUpdating(true)
    setUpdateResult(null)

    try {
      const response = await fetch('/api/update-job-dates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (result.success) {
        setUpdateResult(result)
        setLastUpdate(new Date().toLocaleString())
        toast({
          title: "Success!",
          description: `Updated ${result.updatedCount} job posting dates`,
        })
      } else {
        throw new Error(result.error || 'Update failed')
      }
    } catch (error) {
      console.error('Update error:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update dates',
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-slate-900 mb-4">
            Job Date Update Admin
          </h1>
          <p className="text-xl text-slate-600">
            Manually update job posting dates to keep them fresh
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-6 h-6" />
                Update Job Dates
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <p className="text-slate-600">
                  This will randomly update all job posting dates to be within the last week (1-7 days ago).
                </p>
                
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">What this does:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Updates all job posting dates randomly</li>
                    <li>• Keeps dates within the last 7 days</li>
                    <li>• Makes job listings appear fresh and current</li>
                    <li>• Maintains data integrity and validation</li>
                  </ul>
                </div>

                <Button 
                  onClick={handleUpdateDates}
                  disabled={isUpdating}
                  className="w-full bg-indigo-600 hover:bg-indigo-700"
                  size="lg"
                >
                  {isUpdating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Updating Dates...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Update All Job Dates
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-6 h-6" />
                Update Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {lastUpdate && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    Last Updated
                  </Badge>
                  <span className="text-sm text-slate-600">{lastUpdate}</span>
                </div>
              )}

              {updateResult && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-800">Update Successful</span>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Jobs Updated:</span>
                      <Badge variant="secondary">{updateResult.updatedCount}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Timestamp:</span>
                      <span className="text-sm text-slate-600">
                        {new Date(updateResult.timestamp).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {!updateResult && !lastUpdate && (
                <div className="text-center py-8">
                  <AlertCircle className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-500">No updates performed yet</p>
                  <p className="text-sm text-slate-400">Click the update button to refresh job dates</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="mt-8">
          <Card className="bg-slate-50 border-slate-200">
            <CardContent className="p-6">
              <h3 className="font-semibold text-slate-900 mb-3">Automation Options</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Build Integration</h4>
                  <p className="text-slate-600">
                    Dates are automatically updated during the build process
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">GitHub Actions</h4>
                  <p className="text-slate-600">
                    Automated daily updates via GitHub Actions workflow
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Cron Jobs</h4>
                  <p className="text-slate-600">
                    Server-side automation using cron job scheduling
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
