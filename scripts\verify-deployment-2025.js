#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(path, description) {
  if (fs.existsSync(path)) {
    const stats = fs.statSync(path);
    log(`✓ ${description} (${(stats.size / 1024).toFixed(1)}KB)`, 'green');
    return true;
  } else {
    log(`✗ ${description} missing`, 'red');
    return false;
  }
}

function execCommand(command, description) {
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    log(`✓ ${description}`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`✗ ${description} failed`, 'red');
    return { success: false, error: error.message };
  }
}

function verifyBuild() {
  log('\n🏗️ Verifying Build Files...', 'cyan');
  
  const buildFiles = [
    { path: 'out/index.html', description: 'Homepage' },
    { path: 'out/jobs/index.html', description: 'Jobs page' },
    { path: 'out/apply/index.html', description: 'Apply page' },
    { path: 'out/about/index.html', description: 'About page' },
    { path: 'out/contact/index.html', description: 'Contact page' },
    { path: 'out/jobs.json', description: 'Jobs data' },
    { path: 'out/manifest.webmanifest', description: 'PWA manifest' },
    { path: 'out/robots.txt', description: 'Robots.txt' },
    { path: 'out/sitemap.xml', description: 'Sitemap' }
  ];
  
  let allPassed = true;
  
  for (const file of buildFiles) {
    if (!checkFile(file.path, file.description)) {
      allPassed = false;
    }
  }
  
  return allPassed;
}

function verifySupabaseFunctions() {
  log('\n🚀 Verifying Supabase Functions...', 'cyan');
  
  const result = execCommand('supabase functions list', 'Listing deployed functions');
  
  if (result.success) {
    const functions = ['process-job-application', 'send-telegram-notification', 'upload-cv'];
    let allDeployed = true;
    
    for (const func of functions) {
      if (result.output.includes(func)) {
        log(`✓ ${func} deployed`, 'green');
      } else {
        log(`✗ ${func} not deployed`, 'red');
        allDeployed = false;
      }
    }
    
    return allDeployed;
  }
  
  return false;
}

function verifyDatabase() {
  log('\n📊 Verifying Database...', 'cyan');
  
  const result = execCommand('supabase db diff', 'Checking database status');
  
  if (result.success && result.output.trim() === '') {
    log('✓ Database is up to date', 'green');
    return true;
  } else if (result.success) {
    log('⚠️ Database has pending changes:', 'yellow');
    console.log(result.output);
    return false;
  }
  
  return false;
}

function verifyEnvironment() {
  log('\n⚙️ Verifying Environment...', 'cyan');
  
  const envFile = '.env';
  if (!fs.existsSync(envFile)) {
    log('✗ .env file not found', 'red');
    return false;
  }
  
  const envContent = fs.readFileSync(envFile, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'TELEGRAM_BOT_TOKEN',
    'TELEGRAM_CHAT_ID'
  ];
  
  let allPresent = true;
  
  for (const varName of requiredVars) {
    if (envContent.includes(varName)) {
      log(`✓ ${varName} configured`, 'green');
    } else {
      log(`✗ ${varName} missing`, 'red');
      allPresent = false;
    }
  }
  
  return allPresent;
}

function verifyAnalyticsRemoval() {
  log('\n🔒 Verifying Analytics Removal...', 'cyan');
  
  const filesToCheck = [
    'app/layout.tsx',
    '.htaccess',
    'package.json'
  ];
  
  let allClean = true;
  
  for (const file of filesToCheck) {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      
      const analyticsTerms = [
        'google-analytics',
        'googletagmanager',
        'GA_MEASUREMENT',
        '@vercel/analytics',
        'Analytics'
      ];
      
      let hasAnalytics = false;
      for (const term of analyticsTerms) {
        if (content.includes(term)) {
          log(`⚠️ Found analytics reference in ${file}: ${term}`, 'yellow');
          hasAnalytics = true;
          allClean = false;
        }
      }
      
      if (!hasAnalytics) {
        log(`✓ ${file} clean of analytics`, 'green');
      }
    }
  }
  
  return allClean;
}

function displaySummary(results) {
  log('\n📋 Deployment Verification Summary', 'bright');
  log('=' .repeat(50), 'cyan');
  
  const checks = [
    { name: 'Build Files', result: results.build },
    { name: 'Supabase Functions', result: results.functions },
    { name: 'Database', result: results.database },
    { name: 'Environment', result: results.environment },
    { name: 'Analytics Removal', result: results.analytics }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    const status = check.result ? '✅ PASS' : '❌ FAIL';
    const color = check.result ? 'green' : 'red';
    log(`${status} ${check.name}`, color);
    
    if (!check.result) {
      allPassed = false;
    }
  }
  
  log('\n' + '=' .repeat(50), 'cyan');
  
  if (allPassed) {
    log('🎉 ALL CHECKS PASSED - DEPLOYMENT READY!', 'bright');
    log('\nNext steps:', 'cyan');
    log('1. Upload ./out/ contents to your hosting provider', 'yellow');
    log('2. Test the live application form', 'yellow');
    log('3. Verify Telegram notifications work', 'yellow');
  } else {
    log('⚠️ SOME CHECKS FAILED - REVIEW ISSUES ABOVE', 'red');
    log('\nPlease fix the failed checks before deploying.', 'red');
  }
}

async function main() {
  log('🔍 Skillza Deployment Verification 2025', 'bright');
  log('=' .repeat(50), 'cyan');
  
  const results = {
    build: verifyBuild(),
    functions: verifySupabaseFunctions(),
    database: verifyDatabase(),
    environment: verifyEnvironment(),
    analytics: verifyAnalyticsRemoval()
  };
  
  displaySummary(results);
  
  process.exit(results.build && results.functions && results.database && results.environment && results.analytics ? 0 : 1);
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
