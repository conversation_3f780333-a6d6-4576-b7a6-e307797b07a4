// Client-side environment configuration for static export
// This ensures all environment variables are properly available on the client

export const clientEnv = {
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'https://skillzajobs.com',
  NODE_ENV: process.env.NODE_ENV || 'production'
} as const

// Validation function to ensure required environment variables are present
export function validateClientEnv() {
  const required = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY'] as const
  
  for (const key of required) {
    if (!clientEnv[key]) {
      console.error(`Missing required environment variable: ${key}`)
      return false
    }
  }
  
  return true
}

// Safe environment variable access
export function getClientEnv(key: keyof typeof clientEnv): string {
  const value = clientEnv[key]
  if (!value) {
    console.warn(`Environment variable ${key} is not set`)
  }
  return value
}
