#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Deploying Supabase Edge Functions...');

// Check if supabase CLI is installed
try {
  execSync('supabase --version', { stdio: 'pipe' });
} catch (error) {
  console.error('❌ Supabase CLI not found. Please install it first:');
  console.error('npm install -g supabase');
  process.exit(1);
}

// Check if we're in a Supabase project
if (!fs.existsSync('supabase/config.toml')) {
  console.error('❌ Not in a Supabase project directory');
  process.exit(1);
}

try {
  // Deploy all Edge Functions
  console.log('📦 Deploying Edge Functions...');
  
  const functions = ['process-job-application', 'send-telegram-notification', 'upload-cv'];
  
  for (const func of functions) {
    const funcPath = path.join('supabase', 'functions', func);
    if (fs.existsSync(funcPath)) {
      console.log(`🔄 Deploying ${func}...`);
      execSync(`supabase functions deploy ${func}`, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log(`✅ ${func} deployed successfully`);
    } else {
      console.log(`⚠️  Function ${func} not found, skipping...`);
    }
  }
  
  console.log('🎉 All Edge Functions deployed successfully!');
  
  // Show deployment status
  console.log('\n📊 Deployment Status:');
  execSync('supabase functions list', { stdio: 'inherit' });
  
} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  process.exit(1);
}
