#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Building for Hostinger Premium Static Hosting (Sep 2025)...\n');

// Step 1: Clean previous builds
console.log('1. Cleaning previous builds...');
try {
  if (fs.existsSync('.next')) {
    fs.rmSync('.next', { recursive: true, force: true });
  }
  if (fs.existsSync('out')) {
    fs.rmSync('out', { recursive: true, force: true });
  }
  if (fs.existsSync('dist-static')) {
    fs.rmSync('dist-static', { recursive: true, force: true });
  }
  console.log('✅ Cleaned previous builds');
} catch (error) {
  console.error('❌ Error cleaning builds:', error.message);
}

// Step 2: Skip dependency installation (already installed)
console.log('\n2. Skipping dependency installation (already installed)...');
console.log('✅ Dependencies ready');

// Step 3: Build the static application
console.log('\n3. Building static application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Static application built successfully');
} catch (error) {
  console.error('❌ Error building application:', error.message);
  process.exit(1);
}

// Step 4: Create static deployment package
console.log('\n4. Creating static deployment package...');
try {
  const distDir = 'dist-static';
  if (fs.existsSync(distDir)) {
    fs.rmSync(distDir, { recursive: true, force: true });
  }
  fs.mkdirSync(distDir, { recursive: true });

  // Copy static files from out directory
  if (fs.existsSync('out')) {
    fs.cpSync('out', distDir, { recursive: true });
    console.log('   📁 Copied static files from out/');
  }

  // Create optimized .htaccess for LiteSpeed Web Server
  const htaccessContent = `# Hostinger Premium Static Hosting Optimization (Sep 2025)
# Optimized for LiteSpeed Web Server

RewriteEngine On

# Enable HTTP/2
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# Security headers
Header always set X-Frame-Options "DENY"
Header always set X-Content-Type-Options "nosniff"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set X-XSS-Protection "1; mode=block"

# Enable Brotli compression (LiteSpeed supports this)
<IfModule mod_brotli.c>
    AddOutputFilterByType BROTLI_COMPRESS text/plain
    AddOutputFilterByType BROTLI_COMPRESS text/css
    AddOutputFilterByType BROTLI_COMPRESS text/xml
    AddOutputFilterByType BROTLI_COMPRESS text/javascript
    AddOutputFilterByType BROTLI_COMPRESS application/javascript
    AddOutputFilterByType BROTLI_COMPRESS application/xml
    AddOutputFilterByType BROTLI_COMPRESS application/xhtml+xml
    AddOutputFilterByType BROTLI_COMPRESS application/rss+xml
    AddOutputFilterByType BROTLI_COMPRESS application/atom+xml
    AddOutputFilterByType BROTLI_COMPRESS image/svg+xml
</IfModule>

# Fallback to GZIP if Brotli not available
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Cache static assets (optimized for LiteSpeed)
<FilesMatch "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header set Cache-Control "public, immutable, max-age=31536000"
    Header unset ETag
    FileETag None
</FilesMatch>

# Fix MIME types for CSS and JS files
<FilesMatch "\\.css$">
    ForceType text/css
    Header set Content-Type "text/css"
</FilesMatch>

<FilesMatch "\\.js$">
    ForceType application/javascript
    Header set Content-Type "application/javascript"
</FilesMatch>

<FilesMatch "\\.json$">
    ForceType application/json
    Header set Content-Type "application/json"
</FilesMatch>

# Cache HTML files for shorter period
<FilesMatch "\\.(html|htm)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 hour"
    Header set Cache-Control "public, max-age=3600"
</FilesMatch>

# Handle SPA routing for Next.js
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [QSA,L]

# Remove trailing slashes (optional)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+([^?\s]*?)/+(\?[^\s]*)?\s
RewriteRule ^ /%1%2 [R=301,L]

# Force HTTPS (if SSL is enabled)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Optimize for CDN
Header set Vary "Accept-Encoding"
Header set X-Content-Type-Options "nosniff"
Header set X-Frame-Options "DENY"

# Preload critical resources
<FilesMatch "\\.html$">
    Header add Link "</css/globals.css>; rel=preload; as=style"
    Header add Link "</js/app.js>; rel=preload; as=script"
</FilesMatch>
`;

  fs.writeFileSync(path.join(distDir, '.htaccess'), htaccessContent);
  console.log('   📄 Created optimized .htaccess for LiteSpeed');

  // Create web.config for Windows hosting (backup)
  const webConfigContent = `<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="SPA Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>
    <staticContent>
      <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
    </staticContent>
    <httpCompression>
      <dynamicTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </staticTypes>
    </httpCompression>
  </system.webServer>
</configuration>`;

  fs.writeFileSync(path.join(distDir, 'web.config'), webConfigContent);
  console.log('   📄 Created web.config for Windows hosting');

  // Create CDN optimization file
  const cdnConfig = `# CDN Optimization Configuration
# For Hostinger's integrated CDN

# Critical resources to preload
<link rel="preload" href="/css/globals.css" as="style">
<link rel="preload" href="/js/app.js" as="script">

# DNS prefetch for external resources
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="dns-prefetch" href="//aqvztdxidpfirjvovhyi.supabase.co">

# Preconnect to external domains
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preconnect" href="https://aqvztdxidpfirjvovhyi.supabase.co">
`;

  fs.writeFileSync(path.join(distDir, 'cdn-optimization.html'), cdnConfig);
  console.log('   📄 Created CDN optimization file');

  // Create performance monitoring script
  const perfScript = `// Performance monitoring for Hostinger Premium
(function() {
  // Monitor Core Web Vitals
  function sendToAnalytics(metric) {
    // Send to your analytics service
    console.log('Performance Metric:', metric);
  }

  // Measure LCP
  new PerformanceObserver((entryList) => {
    for (const entry of entryList.getEntries()) {
      sendToAnalytics({
        name: 'LCP',
        value: entry.startTime,
        id: entry.id
      });
    }
  }).observe({entryTypes: ['largest-contentful-paint']});

  // Measure FID
  new PerformanceObserver((entryList) => {
    for (const entry of entryList.getEntries()) {
      sendToAnalytics({
        name: 'FID',
        value: entry.processingStart - entry.startTime,
        id: entry.id
      });
    }
  }).observe({entryTypes: ['first-input']});

  // Measure CLS
  new PerformanceObserver((entryList) => {
    for (const entry of entryList.getEntries()) {
      sendToAnalytics({
        name: 'CLS',
        value: entry.value,
        id: entry.id
      });
    }
  }).observe({entryTypes: ['layout-shift']});
})();
`;

  fs.writeFileSync(path.join(distDir, 'performance.js'), perfScript);
  console.log('   📄 Created performance monitoring script');

  console.log('✅ Static deployment package created in ./dist-static/');
} catch (error) {
  console.error('❌ Error creating deployment package:', error.message);
  process.exit(1);
}

// Step 5: Create deployment instructions
console.log('\n5. Creating deployment instructions...');
const instructions = `# Hostinger Premium Static Hosting Deployment (Sep 2025)

## 🚀 Optimized for Hostinger Premium Plan Features

### Key Optimizations Applied:
- ✅ Static export for maximum performance
- ✅ LiteSpeed Web Server optimization
- ✅ Brotli + GZIP compression
- ✅ Advanced caching strategies
- ✅ CDN-ready configuration
- ✅ HTTP/2 optimization
- ✅ Security headers

## 📁 Files to Upload

Upload the entire contents of the 'dist-static' folder to your Hostinger public_html directory.

## ⚙️ Hostinger Configuration

### 1. Enable LiteSpeed Cache
1. Login to hPanel
2. Go to 'Performance' > 'LiteSpeed Cache'
3. Enable LiteSpeed Cache
4. Configure cache settings for optimal performance

### 2. Enable CDN
1. Go to 'Performance' > 'CDN'
2. Enable Hostinger's integrated CDN
3. Configure CDN settings for global distribution

### 3. Enable HTTP/2
1. Go to 'Advanced' > 'HTTP/2'
2. Enable HTTP/2 for better performance

### 4. Configure PHP (if needed)
1. Go to 'PHP Configurations'
2. Set PHP version to 8.1 or higher
3. Enable OPcache for better performance

## 🔧 Environment Variables

Since this is a static build, environment variables need to be set at build time:

### Build-time Variables (set before building):
- NEXT_PUBLIC_SUPABASE_URL=https://aqvztdxidpfirjvovhyi.supabase.co
- NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
- NEXT_PUBLIC_BASE_URL=https://your-domain.com

### Runtime Configuration:
For dynamic features, you'll need to use client-side configuration or external services.

## 📊 Performance Features

### Caching Strategy:
- Static assets: 1 year cache
- HTML files: 1 hour cache
- Images: 1 year cache with immutable flag

### Compression:
- Brotli compression (primary)
- GZIP compression (fallback)
- Optimized for LiteSpeed Web Server

### CDN Optimization:
- DNS prefetch for external resources
- Preconnect to critical domains
- Resource hints for faster loading

## 🧪 Testing Checklist

### Performance Tests:
- [ ] Page load time < 2 seconds
- [ ] Lighthouse score > 90
- [ ] Core Web Vitals in green
- [ ] Mobile performance optimized

### Functionality Tests:
- [ ] All pages load correctly
- [ ] Navigation works properly
- [ ] Forms submit successfully
- [ ] Images load and display correctly
- [ ] RTL support works

### CDN Tests:
- [ ] CDN is serving static assets
- [ ] Global access works
- [ ] Compression is active
- [ ] Cache headers are correct

## 🚨 Important Notes

1. **API Routes**: Static export doesn't support API routes. For form submissions, use:
   - Client-side form handling
   - External form services (Formspree, Netlify Forms)
   - Serverless functions (if available)

2. **Dynamic Content**: For dynamic content, consider:
   - Client-side data fetching
   - Static generation with ISR
   - External headless CMS

3. **File Uploads**: For file uploads, use:
   - Direct Supabase Storage uploads
   - External file upload services
   - Client-side upload handling

## 📈 Performance Monitoring

The build includes performance monitoring scripts to track:
- Largest Contentful Paint (LCP)
- First Input Delay (FID)
- Cumulative Layout Shift (CLS)

## 🆘 Troubleshooting

### Common Issues:
1. **404 on refresh**: Check .htaccess rewrite rules
2. **Slow loading**: Verify CDN and caching settings
3. **Images not loading**: Check image optimization settings
4. **Forms not working**: Implement client-side form handling

### Support Resources:
- Hostinger Knowledge Base
- LiteSpeed Documentation
- Next.js Static Export Guide

---

**Build Date**: ${new Date().toISOString()}
**Optimized for**: Hostinger Premium Static Hosting
**Next.js Version**: 15.x
**Features**: LiteSpeed, CDN, Brotli, HTTP/2
`;

fs.writeFileSync('HOSTINGER_STATIC_DEPLOYMENT.md', instructions);
console.log('✅ Created HOSTINGER_STATIC_DEPLOYMENT.md');

console.log('\n🎉 Static build completed successfully!');
console.log('\n📦 Static deployment package ready in ./dist-static/');
console.log('📋 See HOSTINGER_STATIC_DEPLOYMENT.md for detailed instructions');
console.log('\n🚀 Optimized for Hostinger Premium Static Hosting!');
console.log('\n💡 Key Features:');
console.log('   • LiteSpeed Web Server optimization');
console.log('   • Brotli + GZIP compression');
console.log('   • Advanced caching strategies');
console.log('   • CDN-ready configuration');
console.log('   • HTTP/2 optimization');
console.log('   • Security headers');
