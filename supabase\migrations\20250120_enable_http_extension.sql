-- Enable http extension for database triggers to call Edge Functions
CREATE EXTENSION IF NOT EXISTS http;

-- Update the trigger function to use the correct http function
CREATE OR REPLACE FUNCTION notify_telegram()
RETURNS TRIGGER AS $$
BEGIN
  -- Use http_post from the http extension
  PERFORM http_post(
    'https://aqvztdxidpfirjvovhyi.functions.supabase.co/process-job-application',
    json_build_object(
      'name', NEW.name,
      'email', NEW.email,
      'phone', NEW.phone,
      'experience', NEW.experience,
      'position', NEW.position,
      'cvUrl', NEW.cv_url,
      'jobId', NEW.job_id,
      'language', NEW.language
    )::text,
    'application/json',
    ARRAY[http_header('Authorization', 'Bearer ' || current_setting('supabase.auth.jwt'))]
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the insert
    RAISE LOG 'Failed to send Telegram notification: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop and recreate the trigger to ensure it uses the updated function
DROP TRIGGER IF EXISTS job_application_insert_trigger ON public.job_applications;

CREATE TRIGGER job_application_insert_trigger
AFTER INSERT ON public.job_applications
FOR EACH ROW
EXECUTE FUNCTION notify_telegram();
