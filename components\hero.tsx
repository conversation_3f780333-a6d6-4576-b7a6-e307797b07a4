"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/lib/language-context"
import Link from "next/link"
import { ArrowRight, Users, Award, Zap } from "lucide-react"

export function Hero() {
  const { language, isRTL } = useLanguage()

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-indigo-900 to-purple-900">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500 rounded-full blur-3xl opacity-20 animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/4 w-64 h-64 bg-purple-500 rounded-full blur-3xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-1/3 w-48 h-48 bg-blue-500 rounded-full blur-3xl opacity-20 animate-pulse delay-1500"></div>
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-10 z-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5" />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" className="text-white/30" />
        </svg>
      </div>

      <div className="relative z-20 container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-6">
            <span className={`inline-block px-4 py-2 rounded-full bg-indigo-500/20 text-indigo-300 text-sm font-medium backdrop-blur-sm ${isRTL ? "font-arabic" : "font-sans"}`}>
              {language === "ar" ? "فرص عمل سبتمبر 2025" : "September 2025 Intake"}
            </span>
          </div>
          
          <h1 className={`text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-8 text-balance leading-tight ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" ? (
              <>
                <span className="block">ابن مستقبلك مع</span>
                <span className="text-indigo-300">سكيلزا</span>
              </>
            ) : (
              <>
                <span className="block">Build your future with</span>
                <span className="text-indigo-300">Skillza</span>
              </>
            )}
          </h1>

          <p className={`text-lg sm:text-xl lg:text-2xl text-white mb-12 max-w-4xl mx-auto text-pretty leading-relaxed ${isRTL ? "font-arabic" : "font-sans"}`}>
            {language === "ar" 
              ? "شريكك المتخصص في التوظيف والموارد البشرية. نوفر أفضل الفرص الوظيفية في جميع القطاعات بالمملكة العربية السعودية."
              : "Your specialized partner in recruitment and human resources. We provide the best job opportunities across all sectors in Saudi Arabia."}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Link href="/jobs">
              <Button
                size="lg"
                className={`bg-white text-indigo-900 hover:bg-slate-100 px-8 py-6 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-white/25 border-0 ${isRTL ? "font-arabic" : "font-sans"}`}
              >
                {language === "ar" ? "تصفح الوظائف" : "Browse Jobs"}
                <ArrowRight className={`ml-2 w-5 h-5 ${isRTL ? "rotate-180" : ""}`} />
              </Button>
            </Link>
            
            <Link href="/apply">
              <Button
                variant="outline"
                size="lg"
                className={`bg-transparent border-2 border-white text-white hover:bg-white/10 px-8 py-6 text-lg font-semibold transition-all duration-300 ${isRTL ? "font-arabic" : "font-sans"}`}
              >
                {language === "ar" ? "تقدم الآن" : "Apply Now"}
              </Button>
            </Link>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className={`p-4 rounded-xl bg-white/10 backdrop-blur-sm ${isRTL ? "text-right" : "text-left"}`}>
              <div className="flex items-center gap-3 mb-2">
                <Users className="text-indigo-300" size={24} />
                <span className="text-2xl font-bold text-white">500+</span>
              </div>
              <p className={`text-slate-300 ${isRTL ? "font-arabic" : "font-sans"}`}>
                {language === "ar" ? "وظيفة متوفرة" : "Jobs Available"}
              </p>
            </div>
            
            <div className={`p-4 rounded-xl bg-white/10 backdrop-blur-sm ${isRTL ? "text-right" : "text-left"}`}>
              <div className="flex items-center gap-3 mb-2">
                <Award className="text-indigo-300" size={24} />
                <span className="text-2xl font-bold text-white">50+</span>
              </div>
              <p className={`text-slate-300 ${isRTL ? "font-arabic" : "font-sans"}`}>
                {language === "ar" ? "شركة شريكة" : "Partner Companies"}
              </p>
            </div>
            
            <div className={`p-4 rounded-xl bg-white/10 backdrop-blur-sm ${isRTL ? "text-right" : "text-left"}`}>
              <div className="flex items-center gap-3 mb-2">
                <Zap className="text-indigo-300" size={24} />
                <span className="text-2xl font-bold text-white">98%</span>
              </div>
              <p className={`text-slate-300 ${isRTL ? "font-arabic" : "font-sans"}`}>
                {language === "ar" ? "نسبة رضا العملاء" : "Client Satisfaction"}
              </p>
            </div>
            
            <div className={`p-4 rounded-xl bg-white/10 backdrop-blur-sm ${isRTL ? "text-right" : "text-left"}`}>
              <div className="flex items-center gap-3 mb-2">
                <Users className="text-indigo-300" size={24} />
                <span className="text-2xl font-bold text-white">10K+</span>
              </div>
              <p className={`text-slate-300 ${isRTL ? "font-arabic" : "font-sans"}`}>
                {language === "ar" ? "مرشح ناجح" : "Successful Candidates"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}