"use client"

import { useLanguage } from "@/lib/language-context"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, Briefcase, Send, Award, Star, Clock, Users } from "lucide-react"
import { motion } from "framer-motion"
import type { Variants } from "framer-motion"

export default function CTASection() {
  const { language, isRTL } = useLanguage()

  // Animation variants
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants: Variants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  // Features with modern icons
  const features = [
    {
      icon: Briefcase,
      title: language === "ar" ? "النمو الوظيفي" : "Career Growth",
      description: language === "ar" ? "تقدم في حياتك المهنية معنا" : "Advance your career with us"
    },
    {
      icon: Award,
      title: language === "ar" ? "التقدير" : "Recognition",
      description: language === "ar" ? "إنجازاتك مهمة" : "Your achievements matter"
    },
    {
      icon: Users,
      title: language === "ar" ? "فريق رائع" : "Great Team",
      description: language === "ar" ? "اعمل مع الأفضل" : "Work with the best"
    },
    {
      icon: Clock,
      title: language === "ar" ? "المرونة" : "Flexibility",
      description: language === "ar" ? "وازن بين العمل والحياة" : "Balance work and life"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-r from-indigo-600 to-purple-600 text-white overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div 
          className="max-w-4xl mx-auto text-center mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.h2 
            className={`text-3xl lg:text-5xl font-bold text-white mb-6 ${isRTL ? "font-arabic" : "font-sans"}`}
            variants={itemVariants}
          >
            {language === "ar" ? "مستعد لخطوة جديدة في مسيرتك المهنية؟" : "Ready for a new step in your career?"}
          </motion.h2>
          
          <motion.p 
            className={`text-xl text-indigo-100 mb-10 max-w-3xl mx-auto ${isRTL ? "font-arabic" : "font-sans"}`}
            variants={itemVariants}
          >
            {language === "ar" 
              ? "انضم إلى شبكة المواهب المتميزة لدينا واكتشف فرص العمل التي تناسب طموحاتك" 
              : "Join our network of exceptional talent and discover job opportunities that match your ambitions"}
          </motion.p>
          
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
            variants={containerVariants}
          >
            {features.map((feature, index) => (
              <motion.div 
                key={index} 
                className="flex flex-col items-center"
                variants={itemVariants}
              >
                <div className="bg-white/20 p-4 rounded-full mb-4 backdrop-blur-sm">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className={`font-semibold text-lg mb-1 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {feature.title}
                </h3>
                <p className={`text-sm opacity-80 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
          
          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center"
            variants={containerVariants}
          >
            <motion.div variants={itemVariants}>
              <Link href="/jobs">
                <Button 
                  size="lg" 
                  className="bg-white text-indigo-900 hover:bg-slate-100 px-8 py-3 text-lg font-semibold transition-all shadow-lg"
                >
                  <Briefcase className="mr-2 h-5 w-5" />
                  {language === "ar" ? "تصفح الوظائف" : "Browse Jobs"}
                  <ArrowRight className={`ml-2 w-5 h-5 ${isRTL ? "rotate-180" : ""}`} />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div variants={itemVariants}>
              <Link href="/apply">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-white text-indigo-900 hover:bg-white/20 px-8 py-3 text-lg font-semibold shadow-lg"
                >
                  <Send className="mr-2 h-5 w-5" />
                  {language === "ar" ? "تقدم الآن" : "Apply Now"}
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}