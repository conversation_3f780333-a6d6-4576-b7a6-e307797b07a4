# 🚀 Skillza - Specialized Recruitment Services

A modern, bilingual (Arabic/English) job application platform with instant Telegram notifications and advanced security features.

## ✨ **Features**

- 🌐 **Bilingual Support** - Arabic and English interface
- 📱 **Instant Telegram Notifications** - Real-time notifications to multiple chats
- 🔒 **Advanced Security** - CSP-compliant with secure file uploads
- 📊 **Google Analytics 4** - Optional analytics integration
- 🎨 **Modern UI** - Responsive design with Tailwind CSS
- ⚡ **Performance Optimized** - Static export for fast loading
- 🔄 **Auto-retry Logic** - Robust error handling and retry mechanisms

## 🏗️ **Tech Stack**

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui
- **Backend**: Supabase (Database, Storage, Edge Functions)
- **Notifications**: Telegram Bot API
- **Deployment**: Static export for any hosting provider
- **Security**: Content Security Policy (CSP)

## 🚀 **Quick Start**

### **1. Environment Setup**
```bash
# Option 1: Use the setup script
node scripts/setup-env.js

# Option 2: Run the batch file (Windows)
setup-env.bat

# Option 3: Run the PowerShell script (Windows)
.\setup-env.ps1
```

### **2. Install Dependencies**
```bash
npm install
```

### **3. Deploy**
```bash
# Deploy Telegram integration and build
node scripts/deploy-simple.js
```

### **4. Upload**
Upload the contents of the `out/` directory to your web server.

## 📁 **Project Structure**

```
skillza-web/
├── app/                    # Next.js app directory
├── components/             # React components
├── lib/                    # Utility libraries
├── scripts/                # Deployment and utility scripts
├── supabase/               # Supabase configuration and functions
├── public/                 # Static assets
├── out/                    # Build output (ready for deployment)
├── .md/                    # Documentation and archive folder
└── README.md               # This file
```

## 🔧 **Configuration**

### **Required Environment Variables**
```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_IDS=chat_id_1,chat_id_2
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### **Optional Environment Variables**
```env
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_ga_measurement_id  # Google Analytics (optional)
```

## 📱 **Telegram Integration**

The platform features advanced Telegram bot integration with:

- ✅ **Instant notifications** to multiple chats
- ✅ **Bilingual messages** (Arabic/English)
- ✅ **CV document attachments** with preview buttons
- ✅ **Retry logic** with exponential backoff
- ✅ **Rate limiting compliance**
- ✅ **Comprehensive error handling**

## 🔒 **Security Features**

- ✅ **Content Security Policy (CSP)** - Prevents XSS attacks
- ✅ **Input validation** - All form data validated
- ✅ **Secure file uploads** - CV files validated and secured
- ✅ **Error handling** - No sensitive data exposed

## 🚀 **Deployment**

### **Static Hosting (Recommended)**
The application is optimized for static hosting with:
- Static export ready for any hosting provider
- CSP configuration included
- Optimized assets and performance

### **Deployment Scripts**
- `scripts/deploy-simple.js` - Simple deployment without database changes
- `scripts/deploy-telegram-integration.js` - Full deployment with database setup
- `scripts/test-integration.js` - Integration testing suite

## 📊 **Performance**

- ⚡ **Fast Loading** - Static export with optimized assets
- 🔄 **Parallel Processing** - Telegram messages sent simultaneously
- 🛡️ **Error Recovery** - Graceful failure handling
- 📈 **Analytics Ready** - Google Analytics 4 integration

## 🧪 **Testing**

```bash
# Run integration tests
node scripts/test-integration.js

# Test the complete flow
# 1. Submit application form
# 2. Check Telegram notifications
# 3. Verify CV upload
# 4. Test bilingual support
```

## 📚 **Documentation**

All detailed documentation, deployment guides, and troubleshooting information is available in the `.md/` folder:

- Deployment guides and checklists
- Troubleshooting documentation
- Configuration examples
- Historical development notes

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 **License**

This project is proprietary software for Skillza recruitment services.

## 🆘 **Support**

For support and questions:
1. Check the documentation in `.md/` folder
2. Review the troubleshooting guides
3. Check Supabase Edge Function logs
4. Verify environment configuration

---

**🎉 Ready to deploy! Your Telegram-integrated job application platform is production-ready!**
