'use client'

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { useForm, Controller, FieldValues } from "react-hook-form" 
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { useLanguage } from "@/lib/language-context"
import type { Translations } from "@/lib/i18n"
import { Loader2, Upload, ArrowLeft, FileText, User, Mail, Phone, Briefcase, Calendar } from "lucide-react"
import Link from "next/link"
import { PhoneInput } from "@/components/ui/phone-input"
import { isValidPhoneNumber } from "react-phone-number-input"
import { supabase } from "@/lib/supabase"
import { logger } from "@/lib/logger"

interface Job {
  id: string
  title: { ar: string; en: string }
  sector: string
  location: { ar: string; en: string }
  description: { ar: string; en: string }
  requirements: { ar: string[]; en: string[] }
  postedDate: string
  type: { ar: string; en: string }
  salary: { ar: string; en: string }
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

const applicationSchema = (t: Translations) => z.object({
  name: z.string().min(1, t.validation?.nameRequired || "Name is required"),
  email: z.string().email(t.validation?.emailInvalid || "Invalid email address"),
  phone: z.string().refine(isValidPhoneNumber, { message: t.validation?.phoneInvalid || "Invalid phone number" }),
  experience: z.string().min(1, t.validation?.experienceRequired || "Experience is required"),
  position: z.string().min(1, t.validation?.positionRequired || "Position is required"),
  cv: z.any(),
  consent: z.boolean().refine((val) => val === true, t.validation?.consentRequired || "You must agree to the terms"),
}).superRefine((data, ctx) => {
  if (!data.cv || data.cv.length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["cv"],
      message: t.validation?.cvRequired || "CV is required",
    });
  }
  if (data.cv && data.cv.length > 0) {
    if (data.cv[0].size > MAX_FILE_SIZE) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["cv"],
        message: t.fileTooLarge || `File size should be less than 5MB.`,
      });
    }
    if (!ALLOWED_FILE_TYPES.includes(data.cv[0].type)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["cv"],
        message: t.invalidFileType || "Only .pdf, .doc, and .docx formats are supported.",
      });
    }
  }
});

export type ApplicationFormValues = z.infer<ReturnType<typeof applicationSchema>>;

export function ApplicationForm({ jobs }: { jobs: Job[] }) {
  const { toast } = useToast();
  const { t, isRTL, language } = useLanguage();
  const searchParams = useSearchParams();
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const cvFileRef = useRef<HTMLInputElement>(null);
  const [cvFileName, setCvFileName] = useState<string | null>(null); // To display the CV file name

  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationSchema(t)),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      experience: "",
      position: "",
      consent: false,
      cv: undefined,
    },
  });

  const { register, handleSubmit, control, formState: { errors, isSubmitting }, reset, setValue, watch } = form;

  const cvValue = watch("cv");

  useEffect(() => {
    if (cvValue && cvValue.length > 0) {
      setCvFileName(cvValue[0].name);
    } else {
      setCvFileName(null);
    }
  }, [cvValue]);

  useEffect(() => {
    const jobId = searchParams.get("job");
    logger.debug('Job pre-selection effect:', { jobId, jobsLoaded: jobs.length > 0 });
    
    if (jobId && jobs.length > 0) {
      const job = jobs.find((j) => j.id === jobId);
      logger.debug('Job found for pre-selection:', { jobId, jobFound: !!job, jobTitle: job?.title[language] });
      
      if (job) {
        setSelectedJob(job);
        // Store the unique job ID as the select value to avoid duplicate keys/values
        setValue("position", job.id);
        logger.success('Job pre-selected successfully', { 
          jobId: job.id, 
          title: job.title[language] 
        });
      } else {
        logger.warn('Job not found:', { jobId, availableJobs: jobs.map(j => j.id) });
      }
    }
  }, [searchParams, jobs, language, setValue]);

  const onSubmit = async (data: ApplicationFormValues) => {
    const submissionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    logger.info('Starting form submission:', { submissionId, selectedJob: selectedJob?.id });

    try {
      const cvFile = data.cv?.[0];
      if (!cvFile) {
        throw new Error(t.validation?.cvRequired || "CV file is missing.");
      }

      logger.debug('CV file validation passed:', { 
        submissionId,
        fileName: cvFile.name, 
        fileSize: cvFile.size, 
        fileType: cvFile.type 
      });

      // Upload CV to Supabase Storage
      const fileName = `cv_${Date.now()}_${cvFile.name.replace(/\s+/g, '_')}`;
      logger.info('Uploading CV to Supabase Storage...', { submissionId });
      
      const { error: uploadError } = await supabase.storage
        .from('cvs')
        .upload(fileName, cvFile);

      if (uploadError) {
        logger.error('CV upload failed:', { submissionId, error: uploadError });
        throw new Error(uploadError.message || 'Failed to upload CV');
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('cvs')
        .getPublicUrl(fileName);

      const cvUrl = urlData.publicUrl;
      logger.success('CV uploaded successfully', { submissionId });

      // Insert application into database (this will trigger Telegram notification)
      const applicationData = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        experience: data.experience,
        position: selectedJob ? selectedJob.title[language] : data.position,
        cv_url: cvUrl,
        job_id: selectedJob ? selectedJob.id : undefined,
        language: language
      };

      logger.debug('Application data prepared:', { 
        submissionId,
        name: applicationData.name,
        email: applicationData.email,
        position: applicationData.position,
        jobId: applicationData.job_id,
        hasCvUrl: !!applicationData.cv_url
      });

      logger.info('Submitting application to database...', { submissionId });
      const { error: insertError } = await supabase
        .from('job_applications')
        .insert([applicationData]);

      if (insertError) {
        logger.error('Application submission failed:', { 
          submissionId, 
          error: insertError 
        });
        throw new Error(insertError.message || 'Failed to submit application');
      }

      logger.success('Application submitted successfully', { submissionId });

      toast({
        title: t.submissionSuccess || "Application Submitted!",
        description: t.submissionSuccessDesc || "Thank you for applying. We will be in touch shortly.",
      });

      reset();
      setCvFileName(null);
      if (cvFileRef.current) {
        cvFileRef.current.value = "";
      }

    } catch (error: unknown) {
      logger.error("Application submission error:", { submissionId, error });
      
      let errorMessage = "An unexpected error occurred";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast({
        title: t.submissionFailed || "Submission Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white py-12">
       <div className="container mx-auto px-4 max-w-4xl">
        <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
          <Link
            href="/jobs"
            className={`inline-flex items-center gap-2 text-slate-600 hover:text-slate-900 transition-colors ${isRTL ? "flex-row-reverse font-arabic" : "font-sans"}`}>

            <ArrowLeft className={`w-4 h-4 ${isRTL ? "transform rotate-180" : ""}`} />
            <span>{language === "ar" ? "العودة إلى الوظائف" : "Back to Jobs"}</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-8">
                 <CardTitle
                  className={`text-3xl lg:text-4xl font-bold text-slate-900 ${isRTL ? "font-arabic" : "font-sans"}`}
                >
                  {language === "ar" ? (
                    <>
                      تقدم بطلب للانضمام إلى <span className="text-indigo-600">سكيلزا</span>
                    </>
                  ) : (
                    <>
                      Apply to <span className="text-indigo-600">Skillza</span>
                    </>
                  )}
                </CardTitle>
                <p className={`text-slate-600 mt-2 ${isRTL ? "font-arabic" : "font-sans"}`}>
                  {language === "ar"
                    ? "انضم إلى شبكة المواهب المتميزة لدينا"
                    : "Join our network of exceptional talent"}
                </p>
              </CardHeader>

              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" dir={isRTL ? "rtl" : "ltr"}>
                  <div className="space-y-2">
                    <Label htmlFor="name"><User size={16} /> {t.name}</Label>
                    <Input id="name" {...register("name")} autoComplete="name" />
                    {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email"><Mail size={16} /> {t.email}</Label>
                    <Input id="email" type="email" {...register("email")} autoComplete="email" />
                    {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone"><Phone size={16} /> {t.phone}</Label>
                    <Controller
                      name="phone"
                      control={control}
                      render={({ field }) => (
                        <PhoneInput
                          {...field}
                          id="phone"
                          autoComplete="tel"
                        />
                      )}
                    />
                    {errors.phone && <p className="text-sm text-red-500">{errors.phone.message}</p>}
                  </div>

                    <div className="space-y-2">
                        <Label htmlFor="experience"><Calendar size={16} /> {t.experience}</Label>
                        <Controller
                            name="experience"
                            control={control}
                            render={({ field }) => (
                                <Select onValueChange={field.onChange} defaultValue={field.value} name="experience">
                                    <SelectTrigger id="experience"><SelectValue placeholder={language === "ar" ? "اختر سنوات الخبرة" : "Select years of experience"} /></SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="0-2">{language === "ar" ? "0-2 سنوات" : "0-2 years"}</SelectItem>
                                        <SelectItem value="3-5">{language === "ar" ? "3-5 سنوات" : "3-5 years"}</SelectItem>
                                        <SelectItem value="6-10">{language === "ar" ? "6-10 سنوات" : "6-10 years"}</SelectItem>
                                        <SelectItem value="10+">{language === "ar" ? "أكثر من 10 سنوات" : "10+ years"}</SelectItem>
                                    </SelectContent>
                                </Select>
                            )}
                        />
                        {errors.experience && <p className="text-sm text-red-500">{errors.experience.message}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="position"><Briefcase size={16} /> {t.position}</Label>
                        <Controller
                            name="position"
                            control={control}
                            render={({ field }) => (
                                <Select
                                  onValueChange={(val) => {
                                    field.onChange(val);
                                    const job = jobs.find((j) => j.id === val);
                                    setSelectedJob(job ?? null);
                                  }}
                                  value={field.value}
                                  name="position"
                                >
                                    <SelectTrigger id="position"><SelectValue placeholder={language === "ar" ? "اختر الوظيفة المرغوبة" : "Select desired position"} /></SelectTrigger>
                                    <SelectContent>
                                        {jobs.map((job) => (
                                          <SelectItem key={job.id} value={job.id}>
                                            {job.title[language]}
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            )}
                        />
                        {errors.position && <p className="text-sm text-red-500">{errors.position.message}</p>}
                    </div>

                  <div className="space-y-2">
                    <Label htmlFor="cv-upload" className="font-medium font-arabic">
                      <div className="flex items-center gap-2">
                        <FileText size={16} />
                        {t.uploadCV}
                      </div>
                    </Label>
                    <Controller
                      name="cv"
                      control={control}
                      render={({ field: { onChange, onBlur, name } }) => (
                        <Input
                          id="cv-upload"
                          type="file"
                          accept=".pdf,.doc,.docx"
                          onChange={(e) => onChange(e.target.files)}
                          onBlur={onBlur}
                          name={name}
                          ref={cvFileRef}
                          className="hidden"
                        />
                      )}
                    />
                    <Label
                      htmlFor="cv-upload"
                      className={`flex items-center gap-2 px-4 py-2 border rounded-md cursor-pointer hover:bg-slate-50 transition-colors ${errors.cv ? "border-red-500" : "border-slate-300"}`}>
                      <Upload className="w-4 h-4" />
                      {cvFileName || (language === "ar" ? "اختر ملف (PDF, DOC, DOCX)" : "Choose file (PDF, DOC, DOCX)")}
                    </Label>
                    {errors.cv && <p className="text-sm text-red-500">{errors.cv.message as string}</p>}
                  </div>

                  <div className="flex items-start space-x-2 flex-row-reverse">
                    <Controller
                      name="consent"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id="consent"
                          name="consent"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          aria-label={t.validation?.consentRequired || "Consent to data processing"}
                        />
                      )}
                    />
                    <Label htmlFor="consent" className="text-sm text-slate-700 leading-relaxed font-arabic text-right">
                       {language === "ar"
                        ? "أوافق على معالجة بياناتي الشخصية وفقاً لسياسة الخصوصية الخاصة بشركة سكيلزا. أفهم أن بياناتي ستُستخدم لأغراض التوظيف فقط وسيتم التعامل معها بسرية تامة."
                        : "I agree to the processing of my personal data in accordance with Skillza's privacy policy. I understand that my data will be used for recruitment purposes only and will be handled with complete confidentiality."}
                    </Label>
                  </div>
                   {errors.consent && <p className="text-sm text-red-500">{errors.consent.message}</p>}

                  <Button type="submit" disabled={isSubmitting} className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 text-lg font-semibold">
                    {isSubmitting ? (
                      <><Loader2 className="w-4 h-4 animate-spin mr-2" />{t.submitting || 'Submitting...'}</>
                    ) : (
                      'Submit Application'
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
           <div className="lg:col-span-1">
            {selectedJob ? (
              <Card className="border-l-4 border-l-indigo-500 sticky top-8">
                <CardHeader className="pb-3">
                  <CardTitle className={`text-xl font-bold text-slate-900 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                    {selectedJob.title[language]}
                  </CardTitle>
                  <p className={`text-slate-600 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                    {selectedJob.sector}
                  </p>
                </CardHeader>
                <CardContent>
                  <div className={`space-y-4 ${isRTL ? "text-right" : ""}`}>
                    <div>
                      <h4 className={`font-medium text-slate-900 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}>
                        {language === "ar" ? "الوصف:" : "Description:"}
                      </h4>
                      <p className={`text-slate-600 ${isRTL ? "font-arabic" : "font-sans"}`}>
                        {selectedJob.description[language]}
                      </p>
                    </div>

                    <div>
                      <h4 className={`font-medium text-slate-900 mb-2 ${isRTL ? "font-arabic" : "font-sans"}`}>
                        {language === "ar" ? "المتطلبات:" : "Requirements:"}
                      </h4>
                      <ul className={`list-disc list-inside text-slate-600 space-y-1 ${isRTL ? "font-arabic" : "font-sans"}`}>
                        {selectedJob.requirements[language].map((req, index) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    <div className={`pt-4 border-t border-slate-200 ${isRTL ? "text-right" : ""}`}>
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`font-medium ${isRTL ? "font-arabic" : "font-sans"}`}>
                          {language === "ar" ? "الموقع:" : "Location:"}
                        </span>
                        <span className={isRTL ? "font-arabic" : "font-sans"}>
                          {selectedJob.location[language]}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${isRTL ? "font-arabic" : "font-sans"}`}>
                          {language === "ar" ? "الراتب:" : "Salary:"}
                        </span>
                        <span className={isRTL ? "font-arabic" : "font-sans"}>
                          {selectedJob.salary[language]}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="bg-indigo-50 border-indigo-100">
                <CardContent className="pt-6">
                  <h3 className={`font-bold text-lg text-indigo-800 mb-2 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                    {language === "ar" ? "كيفية التقديم" : "How to Apply"}
                  </h3>
                  <p className={`text-indigo-700 ${isRTL ? "font-arabic text-right" : "font-sans"}`}>
                    {language === "ar"
                      ? "املأ النموذج بعناية وتأكد من إرفاق سيرتك الذاتية. سنقوم بمراجعة طلبك والتواصل معك قريباً."
                      : "Fill out the form carefully and make sure to attach your CV. We'll review your application and get back to you soon."}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}