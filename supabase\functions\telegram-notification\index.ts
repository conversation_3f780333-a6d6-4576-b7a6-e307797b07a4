import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

// Environment variables with validation
const telegramBotToken = Deno.env.get('TELEGRAM_BOT_TOKEN')
const telegramChatIds = Deno.env.get('TELEGRAM_CHAT_IDS')?.split(',').map(id => id.trim()).filter(Boolean)

// Validate configuration
if (!telegramBotToken || !telegramChatIds || telegramChatIds.length === 0) {
  console.error('❌ Missing Telegram configuration')
  throw new Error('Telegram configuration is incomplete')
}

console.log('✅ Telegram Bot Token:', telegramBotToken ? 'Configured' : 'Missing')
console.log('✅ Telegram Chat IDs:', telegramChatIds?.length || 0)

// CORS headers for web requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

// Enhanced Telegram API client with exponential backoff
class TelegramClient {
  private botToken: string
  private maxRetries = 3
  private baseDelay = 1000

  constructor(botToken: string) {
    this.botToken = botToken
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  async sendMessage(chatId: string, text: string, options: any = {}): Promise<any> {
    const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            chat_id: chatId,
            text,
            parse_mode: 'Markdown',
            disable_web_page_preview: true,
            ...options
          })
        })

        const result = await response.json()
        
        if (result.ok) {
          return result
        } 
        
        // Handle rate limiting
        if (result.error_code === 429) {
          const retryAfter = result.parameters?.retry_after || Math.pow(2, attempt)
          console.log(`⏳ Rate limited, waiting ${retryAfter}s before retry ${attempt + 1}`)
          await this.delay(retryAfter * 1000)
          continue
        }
        
        throw new Error(result.description || 'Unknown Telegram API error')
        
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error.message)
        
        if (attempt < this.maxRetries) {
          const delay = this.baseDelay * Math.pow(2, attempt - 1)
          console.log(`⏳ Retrying in ${delay}ms...`)
          await this.delay(delay)
        } else {
          throw error
        }
      }
    }
  }

  async sendDocument(chatId: string, document: string, caption: string, options: any = {}): Promise<any> {
    const url = `https://api.telegram.org/bot${this.botToken}/sendDocument`
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            chat_id: chatId,
            document,
            caption,
            parse_mode: 'Markdown',
            ...options
          })
        })

        const result = await response.json()
        
        if (result.ok) {
          return result
        }
        
        if (result.error_code === 429) {
          const retryAfter = result.parameters?.retry_after || Math.pow(2, attempt)
          console.log(`⏳ Document rate limited, waiting ${retryAfter}s`)
          await this.delay(retryAfter * 1000)
          continue
        }
        
        throw new Error(result.description || 'Unknown Telegram API error')
        
      } catch (error) {
        console.error(`❌ Document attempt ${attempt} failed:`, error.message)
        
        if (attempt < this.maxRetries) {
          const delay = this.baseDelay * Math.pow(2, attempt - 1)
          await this.delay(delay)
        } else {
          throw error
        }
      }
    }
  }
}

// Create Telegram client instance
const telegram = new TelegramClient(telegramBotToken)

// Message formatter with enhanced localization
function formatApplicationMessage(data: any): string {
  const { name, email, phone, position, experience, cv_url, language = 'en' } = data
  const isArabic = language === 'ar'
  
  const timestamp = new Date().toLocaleString(isArabic ? 'ar-SA' : 'en-US', { 
    timeZone: 'Asia/Riyadh',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  const emoji = isArabic ? '🇸🇦' : '🇺🇸'
  const title = isArabic ? 'طلب توظيف جديد' : 'New Job Application'
  
  return `${emoji} *${title}*

👤 *${isArabic ? 'الاسم' : 'Name'}:* ${name}
📧 *${isArabic ? 'البريد الإلكتروني' : 'Email'}:* ${email}
📱 *${isArabic ? 'الهاتف' : 'Phone'}:* ${phone}
💼 *${isArabic ? 'المنصب' : 'Position'}:* ${position}
🎯 *${isArabic ? 'الخبرة' : 'Experience'}:* ${experience}
${cv_url ? `📄 *${isArabic ? 'السيرة الذاتية' : 'CV'}:* [${isArabic ? 'تحميل' : 'Download'}](${cv_url})` : ''}

_${isArabic ? 'تم الإرسال في' : 'Submitted at'}: ${timestamp}_`
}

// Main handler
serve(async (req) => {
  console.log('🚀 Telegram notification function started')
  console.log('📥 Request method:', req.method)
  
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  
  try {
    // Parse request body
    const body = await req.json()
    console.log('📋 Request body received')
    
    // Extract application data
    const applicationData = body.record || body
    console.log('📝 Application data:', {
      name: applicationData.name,
      email: applicationData.email,
      position: applicationData.position,
      hasCV: !!applicationData.cv_url
    })
    
    // Format message
    const message = formatApplicationMessage(applicationData)
    console.log('💬 Message formatted successfully')
    
    // Send notifications to all chat IDs
    const results = []
    const sendPromises = telegramChatIds.map(async (chatId: string) => {
      try {
        console.log(`📤 Sending to chat: ${chatId}`)
        
        // Send main notification message
        const messageResult = await telegram.sendMessage(chatId, message)
        
        const result = {
          chatId,
          messageId: messageResult.result.message_id,
          status: 'success',
          timestamp: new Date().toISOString()
        }
        
        // Send CV as document if available
        if (applicationData.cv_url) {
          try {
            const isArabic = applicationData.language === 'ar'
            const caption = `📄 ${isArabic ? 'السيرة الذاتية لـ' : 'CV for'} ${applicationData.name} - ${applicationData.position}`
            
            const docResult = await telegram.sendDocument(chatId, applicationData.cv_url, caption, {
              reply_markup: {
                inline_keyboard: [[
                  { 
                    text: `📄 ${isArabic ? 'عرض السيرة الذاتية' : 'View CV'}`, 
                    url: applicationData.cv_url 
                  }
                ]]
              }
            })
            
            result.documentId = docResult.result.message_id
            console.log(`📎 CV sent to chat ${chatId}`)
            
          } catch (docError) {
            console.error(`❌ Failed to send CV to chat ${chatId}:`, docError.message)
            result.documentError = docError.message
            result.status = 'partial'
          }
        }
        
        console.log(`✅ Successfully sent to chat ${chatId}`)
        return result
        
      } catch (error) {
        console.error(`❌ Failed to send to chat ${chatId}:`, error.message)
        return {
          chatId,
          error: error.message,
          status: 'error',
          timestamp: new Date().toISOString()
        }
      }
    })
    
    // Wait for all notifications to complete
    const notificationResults = await Promise.allSettled(sendPromises)
    const finalResults = notificationResults.map(result => 
      result.status === 'fulfilled' ? result.value : {
        error: result.reason?.message || 'Unknown error',
        status: 'error',
        timestamp: new Date().toISOString()
      }
    )
    
    // Calculate success metrics
    const successCount = finalResults.filter(r => r.status === 'success' || r.status === 'partial').length
    const totalCount = finalResults.length
    
    console.log(`📊 Notification summary: ${successCount}/${totalCount} successful`)
    
    // Return response
    return new Response(JSON.stringify({
      success: successCount > 0,
      results: finalResults,
      metrics: {
        successCount,
        totalCount,
        successRate: `${Math.round((successCount / totalCount) * 100)}%`
      },
      message: `Notifications sent to ${successCount}/${totalCount} chats successfully`,
      timestamp: new Date().toISOString()
    }), {
      status: successCount > 0 ? 200 : 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('❌ Critical error in notification function:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
