#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  try {
    log(`\n${colors.cyan}${description}...${colors.reset}`);
    log(`${colors.yellow}Running: ${command}${colors.reset}`);
    
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    log(`${colors.green}✓ ${description} completed successfully${colors.reset}`);
    if (output.trim()) {
      console.log(output);
    }
    return true;
  } catch (error) {
    log(`${colors.red}✗ ${description} failed${colors.reset}`, 'red');
    log(`Error: ${error.message}`, 'red');
    if (error.stdout) {
      console.log('Stdout:', error.stdout);
    }
    if (error.stderr) {
      console.log('Stderr:', error.stderr);
    }
    return false;
  }
}

function optimizeBuild() {
  log('\n⚡ Optimizing build for production...', 'cyan');
  
  // Clean previous builds
  if (fs.existsSync('.next')) {
    log('Cleaning previous build...', 'yellow');
    execCommand('rm -rf .next', 'Cleaning .next directory');
  }
  
  if (fs.existsSync('out')) {
    log('Cleaning previous static export...', 'yellow');
    execCommand('rm -rf out', 'Cleaning out directory');
  }
  
  // Set production environment
  process.env.NODE_ENV = 'production';
  process.env.NEXT_TELEMETRY_DISABLED = '1';
  
  return true;
}

function runPreBuildTasks() {
  log('\n🔧 Running pre-build tasks...', 'cyan');
  
  // Update job dates
  if (fs.existsSync('scripts/update-job-dates.js')) {
    execCommand('node scripts/update-job-dates.js', 'Updating job posting dates');
  }
  
  // Run pre-build script
  if (fs.existsSync('scripts/pre-build.js')) {
    execCommand('node scripts/pre-build.js', 'Running pre-build script');
  }
  
  return true;
}

function buildNextJS() {
  log('\n🏗️ Building Next.js application...', 'cyan');
  
  // Build with static export for hosting
  const result = execCommand('npm run build:static', 'Building static export');
  
  if (!result) {
    // Fallback to regular build
    log('Static build failed, trying regular build...', 'yellow');
    return execCommand('npm run build', 'Building Next.js application');
  }
  
  return result;
}

function optimizeAssets() {
  log('\n🎨 Optimizing assets...', 'cyan');
  
  // Check if out directory exists
  if (!fs.existsSync('out')) {
    log('No out directory found, skipping asset optimization', 'yellow');
    return true;
  }
  
  // Optimize images (if sharp is available)
  try {
    execCommand('npx @squoosh/cli --help', 'Checking image optimization tools', { stdio: 'pipe' });
    log('Image optimization tools available', 'green');
  } catch (error) {
    log('Image optimization tools not available, skipping...', 'yellow');
  }
  
  return true;
}

function generateSitemap() {
  log('\n🗺️ Generating sitemap...', 'cyan');
  
  // Check if sitemap was generated
  if (fs.existsSync('out/sitemap.xml')) {
    log('✓ Sitemap generated successfully', 'green');
  } else {
    log('⚠️ Sitemap not found', 'yellow');
  }
  
  return true;
}

function verifyBuild() {
  log('\n✅ Verifying build...', 'cyan');
  
  const checks = [
    { path: 'out/index.html', description: 'Homepage' },
    { path: 'out/jobs/index.html', description: 'Jobs page' },
    { path: 'out/apply/index.html', description: 'Apply page' },
    { path: 'out/about/index.html', description: 'About page' },
    { path: 'out/contact/index.html', description: 'Contact page' },
    { path: 'out/jobs.json', description: 'Jobs data' },
    { path: 'out/manifest.webmanifest', description: 'PWA manifest' },
    { path: 'out/robots.txt', description: 'Robots.txt' },
    { path: 'out/sitemap.xml', description: 'Sitemap' }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    if (fs.existsSync(check.path)) {
      const stats = fs.statSync(check.path);
      log(`✓ ${check.description} (${(stats.size / 1024).toFixed(1)}KB)`, 'green');
    } else {
      log(`✗ ${check.description} missing`, 'red');
      allPassed = false;
    }
  }
  
  return allPassed;
}

function displayBuildSummary() {
  log('\n📊 Build Summary', 'bright');
  log('=' .repeat(40), 'cyan');
  
  if (fs.existsSync('out')) {
    const outStats = fs.statSync('out');
    log(`Build size: ${(outStats.size / 1024 / 1024).toFixed(2)}MB`, 'yellow');
    
    // Count files
    const countFiles = (dir) => {
      let count = 0;
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
          count += countFiles(filePath);
        } else {
          count++;
        }
      }
      return count;
    };
    
    const fileCount = countFiles('out');
    log(`Total files: ${fileCount}`, 'yellow');
  }
  
  log('\n🚀 Production build completed successfully!', 'green');
  log('Ready for deployment to static hosting.', 'green');
}

async function main() {
  log('🏗️ Production Build 2025', 'bright');
  log('=' .repeat(40), 'cyan');
  
  try {
    // Step 1: Optimize build environment
    if (!optimizeBuild()) {
      log('\n❌ Build optimization failed.', 'red');
      process.exit(1);
    }
    
    // Step 2: Run pre-build tasks
    if (!runPreBuildTasks()) {
      log('\n❌ Pre-build tasks failed.', 'red');
      process.exit(1);
    }
    
    // Step 3: Build Next.js application
    if (!buildNextJS()) {
      log('\n❌ Next.js build failed.', 'red');
      process.exit(1);
    }
    
    // Step 4: Optimize assets
    if (!optimizeAssets()) {
      log('\n⚠️ Asset optimization had issues, but continuing...', 'yellow');
    }
    
    // Step 5: Generate sitemap
    if (!generateSitemap()) {
      log('\n⚠️ Sitemap generation had issues, but continuing...', 'yellow');
    }
    
    // Step 6: Verify build
    if (!verifyBuild()) {
      log('\n⚠️ Build verification found issues, but build completed.', 'yellow');
    }
    
    // Step 7: Display summary
    displayBuildSummary();
    
  } catch (error) {
    log(`\n❌ Unexpected error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
