// Production-safe logging utility
// Only logs errors in production, full logging in development

const isDev = process.env.NODE_ENV === 'development'
const isProd = process.env.NODE_ENV === 'production'

export const logger = {
  // Always log errors
  error: (...args: any[]) => {
    console.error(...args)
  },

  // Only log warnings in development or if explicitly enabled
  warn: (...args: any[]) => {
    if (isDev) {
      console.warn(...args)
    }
  },

  // Only log info in development
  info: (...args: any[]) => {
    if (isDev) {
      console.info(...args)
    }
  },

  // Only log debug in development
  debug: (...args: any[]) => {
    if (isDev) {
      console.log(...args)
    }
  },

  // Production-safe success logging (minimal)
  success: (message: string, data?: any) => {
    if (isDev) {
      console.log('✅', message, data || '')
    } else {
      // In production, only log the essential success message
      console.log('✅', message)
    }
  },

  // Production-safe performance logging
  perf: (message: string, data?: any) => {
    if (isDev) {
      console.log('⚡', message, data || '')
    }
    // No performance logs in production to reduce noise
  }
}

// Legacy console.log replacement for debugging
export const devLog = (...args: any[]) => {
  if (isDev) {
    console.log(...args)
  }
}
