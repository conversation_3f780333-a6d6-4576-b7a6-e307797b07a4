import { createClient } from '@supabase/supabase-js'
import { clientEnv, validateClientEnv } from './client-env'

// Validate environment variables
if (typeof window !== 'undefined' && !validateClientEnv()) {
  console.error('Missing required environment variables for Supabase')
}

// Client-side only Supabase configuration for static export
const supabaseUrl = clientEnv.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = clientEnv.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Single client-side Supabase client to avoid multiple instances
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Disable for static export
    flowType: 'pkce'
  },
  realtime: {
    params: {
      eventsPerSecond: 5 // Reduced for static hosting
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'skillza-web@1.0.0'
    }
  }
})

