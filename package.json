{"name": "skillza-web", "version": "0.1.0", "private": true, "scripts": {"build": "node scripts/pre-build.js && next build", "build:static": "node scripts/pre-build.js && node scripts/build-static-hostinger.js", "deploy:simple": "node scripts/deploy-simple.js", "deploy:full": "node scripts/deploy-telegram-integration.js", "dev": "set FAST_REFRESH=false && next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "setup-bot": "ts-node scripts/setup-telegram-bot.ts", "test-integration": "node scripts/test-integration.js", "update-dates": "node scripts/update-job-dates.js", "update-dates:api": "curl -X POST http://localhost:3000/api/update-job-dates", "deploy:functions": "node scripts/deploy-edge-functions.js", "test:functions": "node scripts/test-edge-functions.js", "setup:supabase": "node scripts/setup-supabase.js", "verify:deployment": "node scripts/verify-deployment-2025.js", "configure:production": "node scripts/configure-production.js"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.12", "@radix-ui/react-alert-dialog": "1.1.15", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.3", "@radix-ui/react-collapsible": "1.1.12", "@radix-ui/react-context-menu": "2.2.16", "@radix-ui/react-dialog": "1.1.15", "@radix-ui/react-dropdown-menu": "2.1.16", "@radix-ui/react-hover-card": "1.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menubar": "1.1.16", "@radix-ui/react-navigation-menu": "1.2.14", "@radix-ui/react-popover": "1.1.15", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.8", "@radix-ui/react-scroll-area": "1.2.10", "@radix-ui/react-select": "2.2.6", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.6", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.6", "@radix-ui/react-tabs": "1.1.13", "@radix-ui/react-toast": "1.2.15", "@radix-ui/react-toggle": "1.1.10", "@radix-ui/react-toggle-group": "1.1.11", "@radix-ui/react-tooltip": "1.2.8", "@supabase/supabase-js": "^2.57.4", "@t3-oss/env-nextjs": "^0.13.8", "@vercel/blob": "^1.1.1", "arktype": "^2.1.22", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "country-code-lookup": "^0.1.3", "critters": "^0.0.25", "date-fns": "^4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.23.12", "geist": "^1.5.1", "input-otp": "1.4.1", "libphonenumber-js": "^1.12.17", "lucide-react": "^0.544.0", "next": "^15.5.3", "next-connect": "^1.0.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-country-flag": "^3.1.0", "react-day-picker": "9.8.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-phone-number-input": "^3.4.12", "react-resizable-panels": "^2.1.9", "recharts": "2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^13.0.0", "valibot": "^1.1.0", "vaul": "^1.1.2", "zod": "^4.1.8"}, "devDependencies": {"@next/eslint-plugin-next": "^15.5.3", "@tailwindcss/postcss": "^4.1.13", "@types/node": "^24.3.1", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "cross-env": "^10.0.0", "dotenv": "^17.2.2", "env-cmd": "^11.0.0", "eslint": "^9.35.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "ts-node": "^10.9.2", "tw-animate-css": "1.3.3", "typescript": "^5.9.2"}}